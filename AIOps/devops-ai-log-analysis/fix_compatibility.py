#!/usr/bin/env python3
"""
Quick fix script for Python version compatibility issues
"""

import sys
import subprocess
import os

def check_python_version():
    """Check Python version and provide recommendations"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9+ required")
        return False
    elif version.major == 3 and version.minor >= 13:
        print("⚠️  Python 3.13+ detected - using compatible package versions")
        return True
    else:
        print("✅ Python version compatible")
        return True

def install_compatible_packages():
    """Install packages compatible with current Python version"""
    print("Installing compatible packages...")
    
    # Basic packages that should work with any Python 3.9+
    basic_packages = [
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "scikit-learn>=1.3.0",
        "flask>=2.3.0",
        "werkzeug>=2.3.0",
        "jinja2>=3.1.0",
        "markupsafe>=2.1.0",
        "requests>=2.31.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "xmltodict>=0.13.0",
        "jsonschema>=4.17.0",
        "tqdm>=4.65.0"
    ]
    
    # Install basic packages first
    try:
        for package in basic_packages:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ Basic packages installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing basic packages: {e}")
        return False
    
    # Try to install advanced packages
    advanced_packages = []
    
    # TensorFlow - version depends on Python version
    if sys.version_info >= (3, 13):
        advanced_packages.append("tensorflow>=2.15.0")
    else:
        advanced_packages.append("tensorflow>=2.8.0")
    
    # PyTorch
    advanced_packages.extend([
        "torch>=2.0.0",
        "nltk>=3.8",
        "pyod>=1.1.0",
        "statsmodels>=0.14.0"
    ])
    
    for package in advanced_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"⚠️  {package} failed - skipping (UI will still work)")
    
    print("✅ Installation complete")
    return True

def main():
    print("DevOps AI Log Analysis - Compatibility Fix")
    print("=" * 50)
    
    if not check_python_version():
        print("\nPlease upgrade to Python 3.9 or higher")
        sys.exit(1)
    
    print("\nInstalling compatible packages...")
    if install_compatible_packages():
        print("\n🎉 Setup complete!")
        print("You can now run: python start_ui.py")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
