#!/usr/bin/env python3
"""
Simple startup script for the DevOps AI Log Analysis Web UI
This version installs only essential dependencies and uses a simplified app.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def install_minimal_requirements():
    """Install only the essential packages for the UI"""
    print("Installing minimal requirements for UI...")
    
    essential_packages = [
        "flask>=2.3.0",
        "werkzeug>=2.3.0",
        "jinja2>=3.1.0",
        "markupsafe>=2.1.0"
    ]
    
    for package in essential_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError as e:
            print(f"Error installing {package}: {e}")
            return False
    
    print("✓ Essential packages installed successfully")
    return True

def create_directories():
    """Create necessary directories"""
    dirs_to_create = [
        "ui/uploads",
        "ui/static",
        "data/processed",
        "data/raw"
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {dir_path}")

def start_simple_ui():
    """Start the simple UI server"""
    print("\n" + "="*60)
    print("🚀 Starting DevOps AI Log Analysis Web UI (Simple Mode)")
    print("="*60)
    print("✓ Server starting on http://localhost:5001")
    print("✓ Press Ctrl+C to stop the server")
    print("="*60)
    
    try:
        os.chdir("ui")
        if os.path.exists("simple_app.py"):
            subprocess.run([sys.executable, "simple_app.py"], check=True)
        else:
            print("✗ simple_app.py not found, trying regular app.py")
            subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n✓ Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Error starting server: {e}")
    except FileNotFoundError:
        print("\n✗ Error: UI app not found. Please make sure you're in the project root directory.")

def main():
    print("DevOps AI Log Analysis - Simple UI Startup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("ui"):
        print("✗ Error: ui directory not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install minimal requirements
    if not install_minimal_requirements():
        print("✗ Failed to install minimal requirements.")
        print("Please try: pip install flask werkzeug jinja2 markupsafe")
        sys.exit(1)
    
    # Wait a moment
    time.sleep(1)
    
    # Start the UI server
    start_simple_ui()

if __name__ == "__main__":
    main()
