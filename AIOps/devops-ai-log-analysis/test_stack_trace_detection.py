#!/usr/bin/env python3
"""
Test the enhanced stack trace detection in the simple UI
"""

import sys
import os

# Add the project path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the analysis function
try:
    from ui.simple_app import simple_log_analysis
except ImportError:
    print("Error: Could not import simple_app. Make sure you're in the project root directory.")
    sys.exit(1)

def test_stack_trace_detection():
    """Test stack trace detection with sample data"""
    
    # Sample log with various types of stack traces
    sample_log = """2025-01-06 10:30:15 [INFO] Application starting up
2025-01-06 10:30:16 [ERROR] Database connection failed
2025-01-06 10:30:17 [ERROR] Java stack trace detected:
java.sql.SQLException: Connection refused
    at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
    at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
    at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
    at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
    at com.example.DatabaseManager.connect(DatabaseManager.java:45)
    at com.example.App.main(App.java:23)
2025-01-06 10:30:18 [ERROR] Python traceback:
Traceback (most recent call last):
  File "/app/main.py", line 123, in process_data
    result = calculate_metrics(data)
  File "/app/utils.py", line 67, in calculate_metrics
    return data['value'] / data['count']
ZeroDivisionError: division by zero
2025-01-06 10:30:19 [ERROR] Another error occurred
2025-01-06 10:30:20 [ERROR] Out of memory error detected
2025-01-06 10:30:21 [INFO] Application attempting recovery"""
    
    print("Testing enhanced stack trace detection...")
    print("=" * 50)
    
    # Run the analysis
    result = simple_log_analysis(sample_log, 'text')
    
    if result['success']:
        print("✅ Analysis completed successfully")
        
        # Check classified errors
        print(f"\n📊 Error Classification:")
        for category, errors in result['classified_errors']['categories'].items():
            print(f"  - {category}: {len(errors)} errors")
            for error in errors[:2]:  # Show first 2 errors
                print(f"    • {error[:80]}...")
        
        print(f"\n🔍 Total Errors Detected: {result['classified_errors']['total_errors']}")
        
        # Check anomalies (especially stack traces)
        print(f"\n🚨 Anomalies Detected: {result['anomalies']['total_anomalies']}")
        for anomaly in result['anomalies']['anomalies_detected']:
            print(f"  - [{anomaly['severity'].upper()}] {anomaly['message']}")
            if 'details' in anomaly:
                details = anomaly['details']
                if details.get('type') == 'multiline_stack_trace':
                    print(f"    Stack trace: {details.get('line_count', 0)} lines, starting at line {details.get('starting_line', 0)}")
        
        # Check root causes
        print(f"\n🎯 Root Cause Analysis:")
        for cause, count in result['root_cause_analysis']['cause_frequency'].items():
            score = result['root_cause_analysis']['priority_scores'].get(cause, 0)
            print(f"  - {cause.replace('_', ' ').title()}: {count} occurrences (Priority: {score:.0f}/100)")
        
        # Check recommendations
        print(f"\n💡 Recommendations:")
        for rec in result['root_cause_analysis']['recommendations']:
            print(f"  - [{rec['priority']}] {rec['action']}")
            print(f"    Impact: {rec['impact']}")
        
        print(f"\n📝 Summary: {result['root_cause_analysis']['summary']}")
        
        # Verify stack trace detection
        stack_trace_anomalies = [
            a for a in result['anomalies']['anomalies_detected'] 
            if 'stack trace' in a['message'].lower() or 'traceback' in a['message'].lower()
        ]
        
        if stack_trace_anomalies:
            print(f"\n✅ Stack trace detection working: {len(stack_trace_anomalies)} stack traces detected")
        else:
            print(f"\n⚠️  No stack traces detected in anomalies")
        
        # Check if stack_trace_errors category exists
        if 'stack_trace_errors' in result['classified_errors']['categories']:
            stack_errors = result['classified_errors']['categories']['stack_trace_errors']
            print(f"✅ Stack trace classification working: {len(stack_errors)} stack trace errors classified")
        else:
            print(f"⚠️  No stack trace errors classified")
            
    else:
        print(f"❌ Analysis failed: {result['error']}")
        return False
    
    return True

def main():
    print("DevOps AI Log Analysis - Stack Trace Detection Test")
    print("=" * 60)
    
    if test_stack_trace_detection():
        print(f"\n🎉 Stack trace detection is working correctly!")
        print(f"The enhanced UI can now detect:")
        print(f"  - Java stack traces (with 'at' lines)")
        print(f"  - Python tracebacks (with File lines)")
        print(f"  - Exception classes and error messages")
        print(f"  - Multiline error blocks")
        print(f"  - Critical error patterns")
    else:
        print(f"\n❌ Stack trace detection test failed")

if __name__ == "__main__":
    main()
