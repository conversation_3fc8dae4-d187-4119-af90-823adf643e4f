#!/usr/bin/env python3
"""
Performance test script for large log files.
This script generates large log files and tests the enhanced processing capabilities.
"""

import sys
import os
import time
import random
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def generate_large_log_file(filename, num_entries=10000, include_multiline=True):
    """Generate a large log file for testing"""
    
    log_levels = ['INFO', 'WARN', 'ERROR', 'DEBUG']
    error_messages = [
        'Database connection failed',
        'HTTP 500 Internal Server Error',
        'Authentication failed: Invalid credentials',
        'Network timeout occurred',
        'SQL query failed: Table does not exist',
        'Out of memory error',
        'Configuration file not found',
        'Permission denied',
        'Service unavailable'
    ]
    
    multiline_errors = [
        '''ERROR Failed to connect to Redis server: Connection timeout
    at RedisClient.connect(RedisClient.java:45)
    at ConnectionPool.getConnection(ConnectionPool.java:23)
    at DataService.retrieveData(DataService.java:67)
    Caused by: java.net.ConnectException: Connection refused''',
        
        '''ERROR NullPointerException in UserController
    java.lang.NullPointerException: Cannot invoke method on null object
    at com.example.UserController.getUser(UserController.java:42)
    at com.example.UserController.handleRequest(UserController.java:28)
    at java.base/java.lang.reflect.Method.invoke(Method.java:566)''',
        
        '''CRITICAL OutOfMemoryError: Java heap space
    at java.base/java.lang.StringConcatenationHelper.newArray(StringConcatenationHelper.java:520)
    at java.base/java.lang.StringConcatenationHelper.simpleConcat(StringConcatenationHelper.java:496)
    at com.example.DataProcessor.processLargeDataSet(DataProcessor.java:156)
    Heap dump created: /tmp/heapdump.hprof'''
    ]
    
    start_time = datetime.now()
    
    print(f"Generating {num_entries} log entries...")
    
    with open(filename, 'w') as f:
        for i in range(num_entries):
            current_time = start_time + timedelta(seconds=i)
            timestamp = current_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 70% normal logs, 30% errors
            if random.random() < 0.7:
                level = random.choice(['INFO', 'DEBUG'])
                message = f"Processing request {i} for user_{random.randint(1000, 9999)}"
            else:
                level = 'ERROR'
                if include_multiline and random.random() < 0.3:
                    # Use multiline error
                    message = random.choice(multiline_errors)
                else:
                    # Use single line error
                    message = random.choice(error_messages)
            
            f.write(f"{timestamp} {level} {message}\n")
            
            # Show progress
            if i % 1000 == 0:
                print(f"  Generated {i:,} entries...")
    
    file_size = os.path.getsize(filename)
    print(f"✓ Generated {filename} with {num_entries:,} entries ({file_size:,} bytes)")
    return file_size

def test_performance():
    """Test processing performance with different file sizes"""
    
    test_files = [
        ('data/raw/small_test.txt', 1000),
        ('data/raw/medium_test.txt', 5000),
        ('data/raw/large_test.txt', 10000),
        ('data/raw/huge_test.txt', 25000)
    ]
    
    # Import processing functions
    from ingestion.ingest_text import ingest_text_logs
    from preprocessing.preprocess_text import preprocess_text
    from error_classification.classify_errors import classify_errors
    from anomaly_detection.detect_anomalies import detect_anomalies
    
    results = []
    
    for filename, num_entries in test_files:
        print(f"\n{'='*60}")
        print(f"Testing with {num_entries:,} entries")
        print(f"{'='*60}")
        
        # Generate test file
        file_size = generate_large_log_file(filename, num_entries)
        
        # Test ingestion
        start_time = time.time()
        logs = ingest_text_logs(filename)
        ingest_time = time.time() - start_time
        
        # Test preprocessing
        start_time = time.time()
        processed_logs = preprocess_text(logs)
        preprocess_time = time.time() - start_time
        
        # Test error classification
        start_time = time.time()
        classified_errors = classify_errors(processed_logs)
        classify_time = time.time() - start_time
        
        # Test anomaly detection (with sample data)
        start_time = time.time()
        sample_data = [{'level': 'ERROR', 'message': 'Test error'}] * min(100, len(processed_logs))
        anomalies = detect_anomalies(sample_data)
        anomaly_time = time.time() - start_time
        
        total_time = ingest_time + preprocess_time + classify_time + anomaly_time
        
        result = {
            'entries': num_entries,
            'file_size': file_size,
            'ingest_time': ingest_time,
            'preprocess_time': preprocess_time,
            'classify_time': classify_time,
            'anomaly_time': anomaly_time,
            'total_time': total_time,
            'total_errors': classified_errors['total_errors'],
            'multiline_errors': classified_errors['multiline_count']
        }
        
        results.append(result)
        
        print(f"📊 Performance Results:")
        print(f"  Ingestion: {ingest_time:.2f}s ({num_entries/ingest_time:.0f} entries/sec)")
        print(f"  Preprocessing: {preprocess_time:.2f}s")
        print(f"  Classification: {classify_time:.2f}s")
        print(f"  Anomaly Detection: {anomaly_time:.2f}s")
        print(f"  Total: {total_time:.2f}s")
        print(f"  Errors Found: {classified_errors['total_errors']}")
        print(f"  Multiline Errors: {classified_errors['multiline_count']}")
        
        # Clean up test file
        os.remove(filename)
    
    # Print summary
    print(f"\n{'='*60}")
    print("PERFORMANCE SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        print(f"{result['entries']:,} entries: {result['total_time']:.2f}s "
              f"({result['entries']/result['total_time']:.0f} entries/sec) "
              f"- {result['total_errors']} errors, {result['multiline_errors']} multiline")

def main():
    print("DevOps AI Log Analysis - Performance Testing")
    print("=" * 60)
    
    # Create directories
    os.makedirs('data/raw', exist_ok=True)
    
    # Run performance tests
    test_performance()
    
    print("\n✅ Performance testing completed!")
    print("\nThe enhanced system can now handle:")
    print("• Large log files efficiently")
    print("• Multiline error detection")
    print("• Advanced pattern matching")
    print("• Detailed root cause analysis")
    print("• Enhanced anomaly detection")

if __name__ == "__main__":
    main()
