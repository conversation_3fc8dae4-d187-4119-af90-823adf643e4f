#!/usr/bin/env python3
"""
Integration Demo: Advanced Anomaly Detection in Main Pipeline
============================================================

This script demonstrates how to integrate the advanced anomaly detection
system into the main DevOps AI log analysis pipeline.
"""

import sys
import os
import time
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import advanced anomaly detection
try:
    from anomaly_detection.advanced_anomaly_detection import (
        detect_anomalies_advanced,
        detect_anomalies_with_monitoring,
        AdvancedAnomalyDetector,
        GenerativeAIAnalyzer,
        RealTimeAnomalyBuffer,
        configure_advanced_detection
    )
    print("✓ Advanced anomaly detection system imported successfully")
except ImportError as e:
    print(f"✗ Failed to import advanced modules: {e}")
    sys.exit(1)

# Import other pipeline components
try:
    from ingestion.ingest_text import ingest_text_logs
    from preprocessing.preprocess_text import preprocess_text_logs
    from error_classification.enhanced_classification import classify_errors_advanced
    from root_cause_analysis.advanced_root_cause import analyze_root_cause_advanced
    print("✓ Pipeline components imported successfully")
except ImportError as e:
    print(f"✗ Failed to import pipeline components: {e}")
    sys.exit(1)

def run_enhanced_pipeline_demo():
    """Demonstrate the enhanced pipeline with advanced anomaly detection"""
    
    print("\n🚀 Enhanced DevOps AI Log Analysis Pipeline Demo")
    print("=" * 60)
    
    # Configuration for advanced anomaly detection
    config = configure_advanced_detection({
        'use_ml': True,
        'use_lstm': False,
        'use_generative_ai': True,
        'real_time_mode': False,
        'contamination': 0.05
    })
    
    print(f"✓ Advanced detection configured: {config}")
    
    # Step 1: Ingest log data
    print("\n📥 Step 1: Ingesting log data...")
    try:
        log_file = 'data/raw/multiline_logs.txt'
        raw_data = ingest_text_logs(log_file, chunk_size=1000)
        print(f"✓ Ingested {len(raw_data)} log entries")
    except Exception as e:
        print(f"✗ Ingestion failed: {e}")
        return False
    
    # Step 2: Preprocess the data
    print("\n🔄 Step 2: Preprocessing log data...")
    try:
        processed_data = preprocess_text_logs(raw_data, enable_multiline=True)
        print(f"✓ Preprocessed {len(processed_data)} log entries")
    except Exception as e:
        print(f"✗ Preprocessing failed: {e}")
        return False
    
    # Step 3: Advanced anomaly detection
    print("\n🔍 Step 3: Advanced anomaly detection...")
    start_time = time.time()
    
    try:
        anomaly_results = detect_anomalies_with_monitoring(
            processed_data,
            use_ml=config['use_ml'],
            use_generative_ai=config['use_generative_ai']
        )
        
        detection_time = time.time() - start_time
        
        print(f"✓ Advanced anomaly detection completed in {detection_time:.3f}s")
        print(f"✓ Found {anomaly_results['count']} anomalies")
        print(f"✓ Severity distribution: {anomaly_results['severity_distribution']}")
        print(f"✓ Detection methods: {list(anomaly_results['detection_methods'].keys())}")
        print(f"✓ Average confidence: {anomaly_results['statistics']['avg_confidence']:.2f}")
        
        # Display AI insights if available
        ai_insights = anomaly_results.get('ai_insights', {})
        if ai_insights:
            print(f"✓ AI insights generated:")
            for category, data in ai_insights.items():
                if isinstance(data, dict) and data:
                    print(f"  - {category}: {len(data)} items")
                elif isinstance(data, list) and data:
                    print(f"  - {category}: {len(data)} items")
        
        # Display AI recommendations
        recommendations = anomaly_results.get('ai_recommendations', [])
        if recommendations:
            print(f"✓ Generated {len(recommendations)} AI recommendations:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"  {i}. {rec.get('category', 'Unknown')}: {rec.get('priority', 'Unknown')} priority")
        
    except Exception as e:
        print(f"✗ Anomaly detection failed: {e}")
        return False
    
    # Step 4: Enhanced error classification
    print("\n🏷️  Step 4: Enhanced error classification...")
    try:
        # Filter error entries for classification
        error_entries = [
            entry for entry in processed_data 
            if isinstance(entry, dict) and entry.get('level', '').upper() in ['ERROR', 'CRITICAL']
        ]
        
        if error_entries:
            classification_results = classify_errors_advanced(error_entries)
            print(f"✓ Classified {len(error_entries)} error entries")
            print(f"✓ Error categories: {list(classification_results.get('categories', {}).keys())}")
        else:
            print("ℹ️  No error entries found for classification")
            
    except Exception as e:
        print(f"✗ Error classification failed: {e}")
        return False
    
    # Step 5: Advanced root cause analysis
    print("\n🔎 Step 5: Advanced root cause analysis...")
    try:
        # Use anomalies for root cause analysis
        if anomaly_results['count'] > 0:
            root_cause_results = analyze_root_cause_advanced(
                anomaly_results['anomalies'][:10]  # Analyze top 10 anomalies
            )
            print(f"✓ Root cause analysis completed")
            print(f"✓ Found {len(root_cause_results.get('potential_causes', []))} potential causes")
            
            # Display top root causes
            causes = root_cause_results.get('potential_causes', [])
            if causes:
                print("✓ Top root causes:")
                for i, cause in enumerate(causes[:3], 1):
                    print(f"  {i}. {cause.get('cause', 'Unknown')} (confidence: {cause.get('confidence', 0):.2f})")
        else:
            print("ℹ️  No anomalies found for root cause analysis")
            
    except Exception as e:
        print(f"✗ Root cause analysis failed: {e}")
        return False
    
    # Step 6: Generate comprehensive report
    print("\n📊 Step 6: Generating comprehensive report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'data_summary': {
            'total_entries': len(processed_data),
            'processing_time': detection_time,
            'throughput': len(processed_data) / detection_time if detection_time > 0 else 0
        },
        'anomaly_detection': {
            'total_anomalies': anomaly_results['count'],
            'severity_distribution': anomaly_results['severity_distribution'],
            'detection_methods': anomaly_results['detection_methods'],
            'performance_metrics': anomaly_results.get('performance_metrics', {}),
            'ai_recommendations': recommendations[:5]  # Top 5 recommendations
        },
        'ai_insights': ai_insights,
        'system_health': {
            'anomaly_rate': anomaly_results['performance_metrics']['anomaly_rate'],
            'critical_rate': anomaly_results['performance_metrics']['critical_rate'],
            'status': 'HEALTHY' if anomaly_results['performance_metrics']['critical_rate'] < 5 else 'ATTENTION_REQUIRED'
        }
    }
    
    # Save report
    try:
        import json
        report_file = f"enhanced_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"✓ Comprehensive report saved to {report_file}")
        
        # Display summary
        print(f"\n📋 Analysis Summary:")
        print(f"  - Total log entries processed: {report['data_summary']['total_entries']}")
        print(f"  - Processing throughput: {report['data_summary']['throughput']:.1f} entries/sec")
        print(f"  - Anomalies detected: {report['anomaly_detection']['total_anomalies']}")
        print(f"  - System status: {report['system_health']['status']}")
        print(f"  - AI recommendations: {len(report['anomaly_detection']['ai_recommendations'])}")
        
    except Exception as e:
        print(f"✗ Report generation failed: {e}")
        return False
    
    print(f"\n🎉 Enhanced pipeline demo completed successfully!")
    return True

def demonstrate_real_time_capabilities():
    """Demonstrate real-time anomaly detection capabilities"""
    
    print("\n⚡ Real-Time Anomaly Detection Demo")
    print("=" * 50)
    
    # Initialize real-time buffer
    buffer = RealTimeAnomalyBuffer(max_size=1000)
    detector = AdvancedAnomalyDetector()
    
    print("Simulating real-time log stream...")
    
    # Simulate incoming logs
    sample_logs = [
        {'timestamp': datetime.now().isoformat(), 'level': 'INFO', 'message': 'Normal operation', 'service': 'web-api'},
        {'timestamp': datetime.now().isoformat(), 'level': 'ERROR', 'message': 'Connection timeout', 'service': 'database'},
        {'timestamp': datetime.now().isoformat(), 'level': 'CRITICAL', 'message': 'OutOfMemoryError', 'service': 'user-service'},
        {'timestamp': datetime.now().isoformat(), 'level': 'ERROR', 'message': 'Unauthorized access attempt', 'service': 'auth-service'},
        {'timestamp': datetime.now().isoformat(), 'level': 'WARN', 'message': 'High CPU usage', 'service': 'web-api'},
    ]
    
    for log in sample_logs:
        buffer.add_entry(log)
        print(f"  📝 Added: {log['level']} - {log['message'][:50]}...")
        time.sleep(0.1)  # Simulate real-time arrival
    
    # Analyze real-time data
    recent_data = buffer.get_recent_entries(minutes=1)
    print(f"\n🔍 Analyzing {len(recent_data)} recent entries...")
    
    try:
        results = detect_anomalies_advanced(recent_data, real_time_mode=True)
        print(f"✓ Real-time analysis found {results['count']} anomalies")
        
        if results['count'] > 0:
            print("⚠️  Real-time alerts:")
            for i, anomaly in enumerate(results['anomalies'][:3], 1):
                severity = anomaly.get('severity', 'UNKNOWN')
                anomaly_type = anomaly.get('type', 'unknown')
                print(f"  {i}. {severity}: {anomaly_type}")
        
        return True
        
    except Exception as e:
        print(f"✗ Real-time analysis failed: {e}")
        return False

def show_performance_comparison():
    """Show performance comparison between basic and advanced detection"""
    
    print("\n📊 Performance Comparison: Basic vs Advanced Detection")
    print("=" * 60)
    
    # Import basic detection for comparison
    try:
        from anomaly_detection.detect_anomalies import detect_anomalies as detect_basic
    except ImportError:
        print("⚠️  Basic detection not available for comparison")
        return True
    
    # Test data sizes
    test_sizes = [100, 500, 1000]
    
    for size in test_sizes:
        print(f"\n📏 Testing with {size} log entries:")
        
        # Generate test data
        test_data = []
        for i in range(size):
            test_data.append({
                'timestamp': datetime.now().isoformat(),
                'level': 'ERROR' if i % 10 == 0 else 'INFO',
                'message': f'Test log entry {i}',
                'service': f'service-{i % 5}'
            })
        
        # Basic detection
        start_time = time.time()
        try:
            basic_results = detect_basic(test_data)
            basic_time = time.time() - start_time
            basic_count = basic_results.get('count', 0)
            print(f"  🔹 Basic:    {basic_count:3d} anomalies in {basic_time:.3f}s ({size/basic_time:.0f} entries/sec)")
        except Exception as e:
            print(f"  🔹 Basic:    Failed - {e}")
            basic_time = float('inf')
            basic_count = 0
        
        # Advanced detection
        start_time = time.time()
        try:
            advanced_results = detect_anomalies_advanced(test_data, use_ml=True, use_generative_ai=False)
            advanced_time = time.time() - start_time
            advanced_count = advanced_results.get('count', 0)
            print(f"  🔸 Advanced: {advanced_count:3d} anomalies in {advanced_time:.3f}s ({size/advanced_time:.0f} entries/sec)")
        except Exception as e:
            print(f"  🔸 Advanced: Failed - {e}")
            advanced_time = float('inf')
            advanced_count = 0
        
        # Comparison
        if basic_time != float('inf') and advanced_time != float('inf'):
            speed_ratio = basic_time / advanced_time
            accuracy_improvement = (advanced_count - basic_count) / max(1, basic_count) * 100
            print(f"  📈 Improvement: {accuracy_improvement:+.1f}% more anomalies, {speed_ratio:.2f}x speed ratio")
    
    return True

if __name__ == "__main__":
    print("🔧 Enhanced DevOps AI Log Analysis System")
    print("=" * 50)
    
    success = True
    
    # Run main pipeline demo
    if not run_enhanced_pipeline_demo():
        success = False
    
    # Demonstrate real-time capabilities
    if not demonstrate_real_time_capabilities():
        success = False
    
    # Show performance comparison
    if not show_performance_comparison():
        success = False
    
    if success:
        print(f"\n🎉 All demonstrations completed successfully!")
        print(f"\nThe enhanced system provides:")
        print(f"  ✓ Advanced ML-based anomaly detection")
        print(f"  ✓ Real-time processing capabilities")
        print(f"  ✓ AI-powered insights and recommendations")
        print(f"  ✓ Enhanced pattern recognition")
        print(f"  ✓ Comprehensive performance monitoring")
        print(f"  ✓ Seamless integration with existing pipeline")
        
        print(f"\n🚀 Your DevOps AI log analysis system is now significantly enhanced!")
    else:
        print(f"\n⚠️  Some demonstrations failed. Please check the errors above.")
    
    sys.exit(0 if success else 1)
