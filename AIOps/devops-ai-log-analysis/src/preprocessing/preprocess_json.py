def preprocess_json(log_data):
    """
    Preprocess JSON log data from ingestion.
    
    Args:
        log_data: List of dictionaries from JSON ingestion
        
    Returns:
        list: Processed log entries
    """
    import json

    # Handle case where log_data is already a list of dictionaries
    if isinstance(log_data, list):
        logs = log_data
    else:
        # Load JSON data if it's a string
        try:
            logs = json.loads(log_data)
        except json.JSONDecodeError as e:
            raise ValueError("Invalid JSON data") from e

    # Extract relevant fields and normalize data
    processed_logs = []
    for log in logs:
        processed_log = {
            "timestamp": log.get("timestamp"),
            "level": log.get("level"),
            "message": log.get("message"),
            "service": log.get("service"),
        }
        processed_logs.append(processed_log)

    return processed_logs

def normalize_timestamps(processed_logs):
    from datetime import datetime

    for log in processed_logs:
        if log["timestamp"]:
            log["timestamp"] = datetime.strptime(log["timestamp"], "%Y-%m-%dT%H:%M:%S.%fZ")
    
    return processed_logs