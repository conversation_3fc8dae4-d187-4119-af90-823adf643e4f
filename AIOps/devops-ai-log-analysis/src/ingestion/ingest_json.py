import json
import os

def ingest_json_logs(file_path):
    """
    Ingest JSON logs from the specified file path.

    Args:
        file_path (str): The path to the JSON log file.

    Returns:
        list: A list of dictionaries containing the extracted log information.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_path} does not exist.")

    with open(file_path, 'r') as file:
        try:
            logs = json.load(file)
        except json.JSONDecodeError as e:
            raise ValueError(f"Error decoding JSON: {e}")

    return logs

def ingest_json(file_path):
    """
    Ingest JSON logs from the specified file path.

    Args:
        file_path (str): The path to the JSON log file.

    Returns:
        list: A list of dictionaries containing the extracted log information.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_path} does not exist.")

    with open(file_path, 'r') as file:
        try:
            logs = json.load(file)
        except json.JSONDecodeError as e:
            raise ValueError(f"Error decoding JSON: {e}")

    return logs

def extract_relevant_info(logs):
    """
    Extract relevant information from the ingested JSON logs.

    Args:
        logs (list): A list of dictionaries containing log information.

    Returns:
        list: A list of dictionaries with relevant fields extracted.
    """
    relevant_logs = []
    for log in logs:
        # Assuming relevant fields are 'timestamp', 'level', and 'message'
        relevant_log = {
            'timestamp': log.get('timestamp'),
            'level': log.get('level'),
            'message': log.get('message')
        }
        relevant_logs.append(relevant_log)

    return relevant_logs