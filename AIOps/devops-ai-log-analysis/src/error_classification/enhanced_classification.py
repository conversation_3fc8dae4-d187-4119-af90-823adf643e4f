import re
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from collections import Counter, defaultdict
import torch
import torch.nn as nn
import torch.nn.functional as F

class TransformerErrorClassifier(nn.Module):
    """Transformer-based error classification model"""
    
    def __init__(self, vocab_size, embed_dim=128, num_heads=8, num_layers=3, num_classes=10):
        super(TransformerErrorClassifier, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.pos_encoding = nn.Parameter(torch.randn(1000, embed_dim))
        
        encoder_layers = nn.TransformerEncoderLayer(
            d_model=embed_dim, 
            nhead=num_heads, 
            dim_feedforward=512,
            dropout=0.1
        )
        self.transformer = nn.TransformerEncoder(encoder_layers, num_layers)
        self.classifier = nn.Linear(embed_dim, num_classes)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        seq_len = x.size(1)
        embeddings = self.embedding(x) + self.pos_encoding[:seq_len]
        transformer_out = self.transformer(embeddings.transpose(0, 1))
        pooled = transformer_out.mean(dim=0)
        output = self.classifier(self.dropout(pooled))
        return F.log_softmax(output, dim=1)

class GenerativeErrorAnalyzer:
    """Generative AI-powered error analysis and explanation"""
    
    def __init__(self):
        self.error_templates = {
            'connection_errors': {
                'patterns': ['connection refused', 'timeout', 'unreachable', 'network error'],
                'root_causes': [
                    'Network connectivity issues between services',
                    'Firewall rules blocking communication',
                    'DNS resolution problems',
                    'Service endpoint unavailability'
                ],
                'solutions': [
                    'Verify network connectivity and routing',
                    'Check firewall configurations',
                    'Validate DNS settings and resolution',
                    'Implement circuit breaker pattern',
                    'Add retry mechanisms with exponential backoff'
                ]
            },
            'database_errors': {
                'patterns': ['sql error', 'database', 'table', 'constraint', 'deadlock'],
                'root_causes': [
                    'Database schema inconsistencies',
                    'Query optimization issues',
                    'Database connection pool exhaustion',
                    'Lock contention and deadlocks'
                ],
                'solutions': [
                    'Review and optimize database queries',
                    'Implement proper indexing strategies',
                    'Configure connection pooling appropriately',
                    'Implement database monitoring and alerting',
                    'Consider database scaling solutions'
                ]
            },
            'authentication_errors': {
                'patterns': ['auth', 'unauthorized', 'forbidden', 'credentials', 'token'],
                'root_causes': [
                    'Invalid or expired credentials',
                    'Misconfigured authentication services',
                    'Token expiration or validation issues',
                    'Role-based access control problems'
                ],
                'solutions': [
                    'Implement robust token refresh mechanisms',
                    'Review authentication service configurations',
                    'Validate RBAC policies and permissions',
                    'Implement comprehensive audit logging',
                    'Add multi-factor authentication where appropriate'
                ]
            },
            'performance_errors': {
                'patterns': ['slow', 'timeout', 'latency', 'performance', 'response time'],
                'root_causes': [
                    'Resource exhaustion (CPU, memory, disk)',
                    'Inefficient algorithms or queries',
                    'Network bandwidth limitations',
                    'Insufficient caching strategies'
                ],
                'solutions': [
                    'Implement performance monitoring and profiling',
                    'Optimize critical code paths and algorithms',
                    'Add caching layers (Redis, Memcached)',
                    'Scale horizontally or vertically',
                    'Implement load balancing and traffic shaping'
                ]
            }
        }
    
    def generate_error_explanation(self, error_message, error_category):
        """Generate detailed explanation for an error"""
        explanation = {
            'error_summary': self._summarize_error(error_message),
            'likely_causes': self._identify_causes(error_message, error_category),
            'impact_assessment': self._assess_impact(error_message, error_category),
            'recommended_actions': self._recommend_actions(error_message, error_category),
            'prevention_strategies': self._suggest_prevention(error_category)
        }
        return explanation
    
    def _summarize_error(self, error_message):
        """AI-generated error summary"""
        # Extract key components
        error_keywords = re.findall(r'\b(error|exception|fail|timeout|refused|denied)\b', 
                                  error_message.lower())
        technical_terms = re.findall(r'\b[A-Z][a-zA-Z]*Exception\b|\b[A-Z]{2,}\b', error_message)
        
        if 'connection' in error_message.lower():
            return f"Service connectivity issue detected: {', '.join(technical_terms[:2])}"
        elif 'database' in error_message.lower() or 'sql' in error_message.lower():
            return f"Database operation failure: {', '.join(technical_terms[:2])}"
        elif 'auth' in error_message.lower():
            return f"Authentication/authorization failure: {', '.join(technical_terms[:2])}"
        else:
            return f"System error detected: {', '.join(error_keywords[:3])}"
    
    def _identify_causes(self, error_message, error_category):
        """Identify likely root causes using AI analysis"""
        causes = []
        
        if error_category in self.error_templates:
            template = self.error_templates[error_category]
            
            # Check for specific patterns in the error message
            for pattern in template['patterns']:
                if pattern in error_message.lower():
                    causes.extend(template['root_causes'][:2])
                    break
        
        # AI-powered pattern analysis
        if 'timeout' in error_message.lower():
            causes.append("Network latency or service response time issues")
        if 'memory' in error_message.lower():
            causes.append("Insufficient system memory or memory leak")
        if 'permission' in error_message.lower():
            causes.append("File system or service permission misconfiguration")
        
        return list(set(causes))  # Remove duplicates
    
    def _assess_impact(self, error_message, error_category):
        """Assess the impact of the error"""
        severity_indicators = {
            'critical': ['critical', 'fatal', 'emergency', 'panic', 'disaster'],
            'high': ['error', 'exception', 'fail', 'timeout', 'unavailable'],
            'medium': ['warn', 'warning', 'slow', 'retry'],
            'low': ['info', 'debug', 'notice']
        }
        
        error_lower = error_message.lower()
        
        for severity, indicators in severity_indicators.items():
            if any(indicator in error_lower for indicator in indicators):
                impact_levels = {
                    'critical': 'Service disruption likely affecting multiple users',
                    'high': 'Significant functionality impairment for users',
                    'medium': 'Minor service degradation with potential user impact',
                    'low': 'Minimal impact on user experience'
                }
                return {
                    'severity': severity,
                    'description': impact_levels[severity],
                    'affected_components': self._identify_affected_components(error_message)
                }
        
        return {
            'severity': 'medium',
            'description': 'Moderate impact requiring investigation',
            'affected_components': ['unknown']
        }
    
    def _identify_affected_components(self, error_message):
        """Identify system components affected by the error"""
        components = []
        
        component_patterns = {
            'database': ['sql', 'database', 'db', 'mysql', 'postgres', 'mongodb'],
            'cache': ['redis', 'memcached', 'cache'],
            'authentication': ['auth', 'login', 'token', 'oauth', 'jwt'],
            'api': ['api', 'rest', 'endpoint', 'http'],
            'messaging': ['queue', 'kafka', 'rabbitmq', 'message'],
            'storage': ['storage', 'disk', 'file', 's3', 'blob']
        }
        
        error_lower = error_message.lower()
        for component, patterns in component_patterns.items():
            if any(pattern in error_lower for pattern in patterns):
                components.append(component)
        
        return components if components else ['application']
    
    def _recommend_actions(self, error_message, error_category):
        """Generate immediate action recommendations"""
        if error_category in self.error_templates:
            return self.error_templates[error_category]['solutions'][:3]
        
        # Fallback recommendations
        return [
            'Investigate system logs for related errors',
            'Check system resource utilization',
            'Verify service configurations and dependencies'
        ]
    
    def _suggest_prevention(self, error_category):
        """Suggest prevention strategies"""
        prevention_strategies = {
            'connection_errors': [
                'Implement health checks and monitoring',
                'Use circuit breaker patterns',
                'Add connection retry mechanisms'
            ],
            'database_errors': [
                'Implement database monitoring and alerting',
                'Regular database performance tuning',
                'Backup and recovery procedures'
            ],
            'authentication_errors': [
                'Regular security audits',
                'Token rotation policies',
                'Access control reviews'
            ]
        }
        
        return prevention_strategies.get(error_category, [
            'Implement comprehensive monitoring',
            'Regular system health checks',
            'Proactive maintenance procedures'
        ])

class EnhancedErrorClassifier:
    """Enhanced error classifier with ML and AI capabilities"""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.gb_classifier = GradientBoostingClassifier(n_estimators=100, random_state=42)
        self.ai_analyzer = GenerativeErrorAnalyzer()
        self.is_trained = False
        
        self.enhanced_categories = {
            'connection_errors': [],
            'database_errors': [],
            'authentication_errors': [],
            'memory_errors': [],
            'network_errors': [],
            'application_errors': [],
            'stack_trace_errors': [],
            'timeout_errors': [],
            'performance_errors': [],
            'security_errors': [],
            'configuration_errors': [],
            'general_errors': []
        }
    
    def extract_ml_features(self, logs):
        """Extract machine learning features from logs"""
        features = []
        for log in logs:
            log_lower = log.lower()
            
            # Feature extraction
            feature_vector = {
                'length': len(log),
                'error_keywords': sum(1 for kw in ['error', 'exception', 'fail'] if kw in log_lower),
                'critical_keywords': sum(1 for kw in ['critical', 'fatal', 'panic'] if kw in log_lower),
                'network_keywords': sum(1 for kw in ['connection', 'timeout', 'network'] if kw in log_lower),
                'database_keywords': sum(1 for kw in ['sql', 'database', 'query'] if kw in log_lower),
                'auth_keywords': sum(1 for kw in ['auth', 'login', 'token'] if kw in log_lower),
                'performance_keywords': sum(1 for kw in ['slow', 'latency', 'performance'] if kw in log_lower),
                'has_stacktrace': int('at ' in log and ('Exception' in log or 'Error' in log)),
                'has_numbers': len(re.findall(r'\d+', log)),
                'has_timestamps': int(bool(re.search(r'\d{4}-\d{2}-\d{2}', log))),
                'line_count': len(log.split('\n'))
            }
            features.append(list(feature_vector.values()))
        
        return np.array(features)
    
    def classify_errors_enhanced(self, logs):
        """Enhanced error classification with AI analysis"""
        # Traditional rule-based classification
        traditional_classification = self._classify_traditional(logs)
        
        # Add AI-powered analysis
        enhanced_results = {
            **traditional_classification,
            'ai_analysis': {},
            'detailed_explanations': {},
            'severity_scores': {},
            'ml_confidence_scores': {}
        }
        
        # Generate AI analysis for each category
        for category, errors in traditional_classification['categories'].items():
            if errors:
                # Generate AI analysis for the category
                sample_error = errors[0] if errors else ""
                ai_explanation = self.ai_analyzer.generate_error_explanation(sample_error, category)
                enhanced_results['ai_analysis'][category] = ai_explanation
                
                # Calculate enhanced severity scores
                enhanced_results['severity_scores'][category] = self._calculate_enhanced_severity(
                    category, errors, ai_explanation
                )
        
        return enhanced_results
    
    def _classify_traditional(self, logs):
        """Traditional rule-based classification with enhanced patterns"""
        # Enhanced single line error patterns
        enhanced_patterns = {
            'connection_errors': [
                'connection', 'connect', 'redis', 'timeout', 'unreachable', 'refused',
                'network error', 'connection reset', 'connection lost'
            ],
            'database_errors': [
                'sql', 'database', 'table', 'db', 'query', 'constraint', 'deadlock',
                'duplicate key', 'foreign key', 'transaction'
            ],
            'authentication_errors': [
                'auth', 'credentials', 'login', 'unauthorized', 'forbidden', 'token',
                'permission denied', 'access denied', 'invalid user'
            ],
            'memory_errors': [
                'memory', 'disk space', 'storage', 'heap', 'oom', 'out of memory',
                'memory leak', 'stack overflow', 'buffer overflow'
            ],
            'network_errors': [
                'network', 'http', '500', '404', '503', 'dns', 'host', 'ssl',
                'certificate', 'tls', 'proxy'
            ],
            'timeout_errors': [
                'timeout', 'timed out', 'deadline exceeded', 'request timeout',
                'connection timeout', 'read timeout'
            ],
            'performance_errors': [
                'slow', 'latency', 'performance', 'response time', 'degradation',
                'bottleneck', 'high cpu', 'high memory'
            ],
            'security_errors': [
                'security', 'breach', 'attack', 'malicious', 'injection', 'exploit',
                'vulnerability', 'unauthorized access'
            ],
            'configuration_errors': [
                'config', 'configuration', 'property', 'setting', 'parameter',
                'environment', 'missing property', 'invalid config'
            ],
            'application_errors': [
                'null pointer', 'segmentation fault', 'assertion', 'panic',
                'runtime error', 'logic error'
            ]
        }
        
        # Multiline patterns for complex errors
        multiline_patterns = {
            'java_stack_trace': re.compile(r'(Exception|Error).*\n(\s+at\s+.*\n?)+', re.MULTILINE),
            'python_traceback': re.compile(r'Traceback \(most recent call last\):.*\n(.*\n)*.*Error:.*', re.MULTILINE),
            'sql_error_block': re.compile(r'SQL.*Error.*\n(.*\n)*.*Query:.*', re.MULTILINE | re.IGNORECASE),
            'connection_timeout': re.compile(r'(Connection|Timeout).*\n(.*timeout.*\n?)*', re.MULTILINE | re.IGNORECASE)
        }
        
        multiline_errors = []
        
        for i, log in enumerate(logs):
            log_lower = log.lower()
            
            # Check for multiline patterns first
            is_multiline = False
            for pattern_name, pattern in multiline_patterns.items():
                if pattern.search(log):
                    multiline_errors.append({
                        'index': i,
                        'type': pattern_name,
                        'content': log,
                        'line_count': len(log.split('\n'))
                    })
                    self.enhanced_categories['stack_trace_errors'].append(log)
                    is_multiline = True
                    break
            
            # If not multiline, check single line patterns
            if not is_multiline and 'ERROR' in log:
                categorized = False
                for category, keywords in enhanced_patterns.items():
                    if any(keyword in log_lower for keyword in keywords):
                        self.enhanced_categories[category].append(log)
                        categorized = True
                        break
                
                if not categorized:
                    self.enhanced_categories['general_errors'].append(log)
        
        # Calculate severity scores
        severity_scores = {}
        for category, errors in self.enhanced_categories.items():
            if errors:
                severity_scores[category] = self._calculate_severity_score(category, errors)
        
        # Convert to counts for summary
        error_summary = {category: len(errors) for category, errors in self.enhanced_categories.items()}
        
        return {
            'categories': self.enhanced_categories,
            'multiline_errors': multiline_errors,
            'severity_scores': severity_scores,
            'summary': error_summary,
            'total_errors': sum(error_summary.values()),
            'multiline_count': len(multiline_errors)
        }
    
    def _calculate_enhanced_severity(self, category, errors, ai_explanation):
        """Calculate enhanced severity score using AI analysis"""
        base_severity = self._calculate_severity_score(category, errors)
        
        # Adjust based on AI analysis
        impact_severity = ai_explanation.get('impact_assessment', {}).get('severity', 'medium')
        severity_multipliers = {
            'critical': 1.5,
            'high': 1.2,
            'medium': 1.0,
            'low': 0.8
        }
        
        multiplier = severity_multipliers.get(impact_severity, 1.0)
        enhanced_severity = min(10, base_severity * multiplier)
        
        return enhanced_severity
    
    def _calculate_severity_score(self, category, errors):
        """Calculate severity score based on error category and patterns"""
        severity_weights = {
            'stack_trace_errors': 9,
            'security_errors': 9,
            'application_errors': 8,
            'database_errors': 7,
            'memory_errors': 7,
            'authentication_errors': 6,
            'performance_errors': 6,
            'network_errors': 5,
            'timeout_errors': 5,
            'configuration_errors': 4,
            'connection_errors': 4,
            'general_errors': 3
        }
        
        base_score = severity_weights.get(category, 3)
        error_count = len(errors)
        
        # Increase severity based on frequency
        if error_count > 20:
            multiplier = 1.8
        elif error_count > 10:
            multiplier = 1.5
        elif error_count > 5:
            multiplier = 1.2
        else:
            multiplier = 1.0
        
        return min(10, base_score * multiplier)

# Updated main classification function
def classify_errors(logs):
    """Enhanced error classification with AI capabilities"""
    classifier = EnhancedErrorClassifier()
    return classifier.classify_errors_enhanced(logs)
