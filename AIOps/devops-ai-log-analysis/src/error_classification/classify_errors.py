from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report
from sklearn.feature_extraction.text import TfidfVectorizer
import pandas as pd
import joblib
import re

class ErrorClassifier:
    def __init__(self, model=None):
        self.model = model if model else RandomForestClassifier()

    def train(self, X, y):
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        self.model.fit(X_train, y_train)
        y_pred = self.model.predict(X_test)
        print(classification_report(y_test, y_pred))

    def predict(self, X):
        return self.model.predict(X)

    def save_model(self, filepath):
        joblib.dump(self.model, filepath)

    def load_model(self, filepath):
        self.model = joblib.load(filepath)

def classify_errors(logs):
    """
    Classify errors from preprocessed logs using rule-based approach with multiline support.
    
    Args:
        logs (list): List of preprocessed log entries (may include multiline entries)
        
    Returns:
        dict: Classification results with error categories, multiline blocks, and counts
    """
    import re
    
    error_categories = {
        'connection_errors': [],
        'database_errors': [],
        'authentication_errors': [],
        'memory_errors': [],
        'network_errors': [],
        'application_errors': [],
        'stack_trace_errors': [],
        'timeout_errors': [],
        'general_errors': []
    }
    
    # Enhanced patterns for multiline error detection
    multiline_patterns = {
        'java_stack_trace': re.compile(r'(Exception|Error).*\n(\s+at\s+.*\n?)+', re.MULTILINE),
        'python_traceback': re.compile(r'Traceback \(most recent call last\):.*\n(.*\n)*.*Error:.*', re.MULTILINE),
        'sql_error_block': re.compile(r'SQL.*Error.*\n(.*\n)*.*Query:.*', re.MULTILINE | re.IGNORECASE),
        'connection_timeout': re.compile(r'(Connection|Timeout).*\n(.*timeout.*\n?)*', re.MULTILINE | re.IGNORECASE)
    }
    
    # Single line error patterns
    single_line_patterns = {
        'connection_errors': ['connection', 'connect', 'redis', 'timeout', 'unreachable'],
        'database_errors': ['sql', 'database', 'table', 'db', 'query', 'constraint'],
        'authentication_errors': ['auth', 'credentials', 'login', 'unauthorized', 'forbidden'],
        'memory_errors': ['memory', 'disk space', 'storage', 'heap', 'oom', 'out of memory'],
        'network_errors': ['network', 'http', '500', '404', '503', 'dns', 'host'],
        'timeout_errors': ['timeout', 'timed out', 'deadline exceeded'],
        'application_errors': ['null pointer', 'segmentation fault', 'assertion', 'panic']
    }
    
    multiline_errors = []
    
    for i, log in enumerate(logs):
        log_lower = log.lower()
        
        # Check for multiline patterns first
        is_multiline = False
        for pattern_name, pattern in multiline_patterns.items():
            if pattern.search(log):
                multiline_errors.append({
                    'index': i,
                    'type': pattern_name,
                    'content': log,
                    'line_count': len(log.split('\n'))
                })
                error_categories['stack_trace_errors'].append(log)
                is_multiline = True
                break
        
        # If not multiline, check single line patterns
        if not is_multiline and 'ERROR' in log:
            categorized = False
            for category, keywords in single_line_patterns.items():
                if any(keyword in log_lower for keyword in keywords):
                    error_categories[category].append(log)
                    categorized = True
                    break
            
            if not categorized:
                error_categories['general_errors'].append(log)
    
    # Calculate severity scores
    severity_scores = {}
    for category, errors in error_categories.items():
        if errors:
            severity_scores[category] = calculate_severity_score(category, errors)
    
    # Convert to counts for summary
    error_summary = {category: len(errors) for category, errors in error_categories.items()}
    
    return {
        'categories': error_categories,
        'multiline_errors': multiline_errors,
        'severity_scores': severity_scores,
        'summary': error_summary,
        'total_errors': sum(error_summary.values()),
        'multiline_count': len(multiline_errors)
    }

def calculate_severity_score(category, errors):
    """Calculate severity score based on error category and patterns"""
    severity_weights = {
        'stack_trace_errors': 9,
        'application_errors': 8,
        'database_errors': 7,
        'authentication_errors': 6,
        'network_errors': 5,
        'timeout_errors': 5,
        'connection_errors': 4,
        'memory_errors': 7,
        'general_errors': 3
    }
    
    base_score = severity_weights.get(category, 3)
    error_count = len(errors)
    
    # Increase severity based on frequency
    if error_count > 10:
        multiplier = 1.5
    elif error_count > 5:
        multiplier = 1.2
    else:
        multiplier = 1.0
    
    return min(10, base_score * multiplier)