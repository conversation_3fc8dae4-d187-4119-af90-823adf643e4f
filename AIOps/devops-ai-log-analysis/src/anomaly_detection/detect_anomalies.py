import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

class AnomalyDetector:
    def __init__(self, contamination=0.05):
        self.contamination = contamination
        self.model = IsolationForest(contamination=self.contamination)

    def fit(self, data):
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        self.model.fit(scaled_data)

    def detect_anomalies(self, data):
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        anomalies = self.model.predict(scaled_data)
        return anomalies

    def evaluate(self, data, true_labels):
        predictions = self.detect_anomalies(data)
        # Evaluation logic can be added here (e.g., precision, recall)
        return predictions

def detect_anomalies(processed_data, window_size=100, threshold_multiplier=2.0):
    """
    Detect anomalies in processed log data with enhanced multiline and pattern detection.
    
    Args:
        processed_data: Processed log data (list of dictionaries or DataFrame)
        window_size: Size of sliding window for anomaly detection
        threshold_multiplier: Multiplier for anomaly threshold
        
    Returns:
        dict: Enhanced anomaly detection results
    """
    import re
    from collections import Counter, defaultdict
    from datetime import datetime, timedelta
    
    if not processed_data:
        return {'anomalies': [], 'summary': 'No data to analyze'}
    
    anomalies = []
    
    # Pattern-based anomaly detection
    suspicious_patterns = {
        'repeated_errors': re.compile(r'(.+ERROR.+)(\n\1){3,}', re.MULTILINE),
        'memory_leak': re.compile(r'(OutOfMemoryError|MemoryError|heap)', re.IGNORECASE),
        'security_breach': re.compile(r'(unauthorized|breach|attack|malicious|injection)', re.IGNORECASE),
        'cascade_failure': re.compile(r'(ERROR.*\n){5,}', re.MULTILINE),
        'unusual_response_time': re.compile(r'response.*time.*([5-9]\d{3,}|[1-9]\d{4,})', re.IGNORECASE)
    }
    
    # Time-based anomaly detection
    error_frequency = defaultdict(list)
    
    if isinstance(processed_data, list):
        for i, log_entry in enumerate(processed_data):
            if isinstance(log_entry, dict):
                timestamp = log_entry.get('timestamp', '')
                level = log_entry.get('level', '').upper()
                message = log_entry.get('message', '')
                
                # Check for critical errors
                if level == 'ERROR':
                    error_frequency[timestamp].append(message)
                    
                    # Check for suspicious patterns
                    full_text = f"{timestamp} {level} {message}"
                    for pattern_name, pattern in suspicious_patterns.items():
                        if pattern.search(full_text):
                            anomalies.append({
                                'index': i,
                                'type': f'pattern_{pattern_name}',
                                'severity': 'HIGH',
                                'data': log_entry,
                                'pattern_matched': pattern_name
                            })
                
                # Check for unusual patterns
                if any(keyword in message.lower() for keyword in ['critical', 'fatal', 'emergency', 'panic']):
                    anomalies.append({
                        'index': i,
                        'type': 'critical_error',
                        'severity': 'CRITICAL',
                        'data': log_entry
                    })
                
                # Check for failure patterns
                if 'timeout' in message.lower() or 'failed' in message.lower():
                    anomalies.append({
                        'index': i,
                        'type': 'failure_pattern',
                        'severity': 'MEDIUM',
                        'data': log_entry
                    })
    
    # Frequency-based anomaly detection
    error_spike_threshold = 5  # More than 5 errors in same timestamp
    for timestamp, errors in error_frequency.items():
        if len(errors) > error_spike_threshold:
            anomalies.append({
                'type': 'error_spike',
                'severity': 'HIGH',
                'timestamp': timestamp,
                'error_count': len(errors),
                'errors': errors[:3]  # Show first 3 errors
            })
    
    # Sliding window anomaly detection for large datasets
    if len(anomalies) > window_size:
        anomalies = sliding_window_filter(anomalies, window_size, threshold_multiplier)
    
    # Calculate anomaly statistics
    severity_counts = Counter([a.get('severity', 'UNKNOWN') for a in anomalies])
    type_counts = Counter([a.get('type', 'UNKNOWN') for a in anomalies])
    
    return {
        'anomalies': anomalies,
        'count': len(anomalies),
        'severity_distribution': dict(severity_counts),
        'type_distribution': dict(type_counts),
        'summary': f'Detected {len(anomalies)} anomalies in the processed data',
        'high_severity_count': severity_counts.get('HIGH', 0) + severity_counts.get('CRITICAL', 0)
    }

def sliding_window_filter(anomalies, window_size, threshold_multiplier):
    """
    Filter anomalies using sliding window to handle large datasets efficiently.
    """
    if len(anomalies) <= window_size:
        return anomalies
    
    # Sort anomalies by severity and keep top anomalies
    severity_priority = {'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
    
    sorted_anomalies = sorted(
        anomalies,
        key=lambda x: severity_priority.get(x.get('severity', 'LOW'), 1),
        reverse=True
    )
    
    # Keep top anomalies within window size
    return sorted_anomalies[:window_size]

# Example usage:
# if __name__ == "__main__":
#     # Load your data here
#     data = pd.read_csv('path_to_your_data.csv')
#     detector = AnomalyDetector()
#     detector.fit(data)
#     anomalies = detector.detect_anomalies(data)
#     print(f"Detected {sum(anomalies == -1)} anomalies out of {len(data)} data points.")