import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest, RandomForestClassifier, VotingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.cluster import DBSCAN, KMeans
from sklearn.metrics import silhouette_score, accuracy_score
from sklearn.decomposition import PCA
from sklearn.svm import OneClassSVM
from sklearn.neural_network import MLPClassifier
import torch
import torch.nn as nn
import torch.optim as optim
from collections import Counter, defaultdict, deque
import re
import json
from datetime import datetime, timedelta
import hashlib
import threading
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

@dataclass
class AnomalyResult:
    """Data class for structured anomaly results"""
    index: int
    type: str
    severity: str
    confidence: float
    data: Dict[str, Any]
    detection_method: str
    timestamp: str = ""
    pattern_matched: str = ""
    risk_score: float = 0.0
    explanation: str = ""
    recommendation: str = ""

class RealTimeAnomalyBuffer:
    """Real-time circular buffer for streaming anomaly detection"""
    
    def __init__(self, max_size: int = 10000):
        self.buffer = deque(maxlen=max_size)
        self.lock = threading.Lock()
        self.timestamps = deque(maxlen=max_size)
        
    def add_entry(self, entry: Dict[str, Any]):
        """Thread-safe addition of log entries"""
        with self.lock:
            self.buffer.append(entry)
            self.timestamps.append(datetime.now())
    
    def get_recent_entries(self, minutes: int = 5) -> List[Dict[str, Any]]:
        """Get entries from the last N minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        with self.lock:
            recent_entries = []
            for entry, timestamp in zip(self.buffer, self.timestamps):
                if timestamp >= cutoff_time:
                    recent_entries.append(entry)
            return recent_entries

class AdvancedAnomalyDetector:
    """Advanced anomaly detection using multiple ML algorithms and ensemble methods"""
    
    def __init__(self, contamination=0.05, enable_gpu=False):
        self.contamination = contamination
        self.enable_gpu = enable_gpu
        
        # Multiple ML models for ensemble detection
        self.isolation_forest = IsolationForest(
            contamination=contamination, 
            random_state=42, 
            n_estimators=200,
            max_samples=0.8
        )
        self.one_class_svm = OneClassSVM(nu=contamination, kernel='rbf', gamma='scale')
        self.dbscan = DBSCAN(eps=0.5, min_samples=5)
        self.kmeans = KMeans(n_clusters=5, random_state=42)
        
        # Scalers and encoders
        self.robust_scaler = RobustScaler()
        self.standard_scaler = StandardScaler()
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000, 
            stop_words='english',
            ngram_range=(1, 3),
            max_df=0.95,
            min_df=2
        )
        self.count_vectorizer = CountVectorizer(max_features=1000)
        
        # Neural network for deep anomaly detection
        self.mlp_classifier = MLPClassifier(
            hidden_layer_sizes=(100, 50, 25),
            max_iter=1000,
            random_state=42,
            early_stopping=True
        )
        
        # Feature importance tracking
        self.feature_importance = {}
        self.is_fitted = False
        self.model_performance = {}
        
        # Real-time processing
        self.real_time_buffer = RealTimeAnomalyBuffer()
        self.anomaly_history = defaultdict(list)
        
        # Advanced pattern recognition
        self.pattern_cache = {}
        self.adaptive_thresholds = {}
        
    def extract_deep_features(self, log_data: List[Dict[str, Any]]) -> np.ndarray:
        """Extract comprehensive feature set from log data"""
        features = []
        text_features = []
        
        for log_entry in log_data:
            if isinstance(log_entry, dict):
                timestamp = log_entry.get('timestamp', '')
                level = log_entry.get('level', '')
                message = log_entry.get('message', '')
                service = log_entry.get('service', '')
                
                # Temporal features
                temporal_features = self._extract_temporal_features(timestamp)
                
                # Text features
                text_features.append(message)
                message_features = self._extract_message_features(message)
                
                # Service and level features
                service_features = self._extract_service_features(service, level)
                
                # Network and system features
                network_features = self._extract_network_features(message)
                
                # Combine all features
                combined_features = (
                    temporal_features + 
                    message_features + 
                    service_features + 
                    network_features
                )
                
                features.append(combined_features)
            else:
                # Handle string entries
                text_features.append(str(log_entry))
                features.append([0] * 25)  # Default feature vector
        
        # Convert to numpy array
        numerical_features = np.array(features)
        
        # Add text vectorization features
        if text_features:
            try:
                tfidf_features = self.tfidf_vectorizer.fit_transform(text_features)
                # Use dimensionality reduction for large feature spaces
                if tfidf_features.shape[1] > 100:
                    pca = PCA(n_components=100)
                    tfidf_features = pca.fit_transform(tfidf_features.toarray())
                else:
                    tfidf_features = tfidf_features.toarray()
                
                # Combine numerical and text features
                combined_features = np.hstack([numerical_features, tfidf_features])
                return combined_features
            except Exception as e:
                print(f"Text vectorization failed: {e}")
                return numerical_features
        
        return numerical_features
    
    def _extract_temporal_features(self, timestamp: str) -> List[float]:
        """Extract time-based features"""
        try:
            if timestamp:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return [
                    dt.hour,
                    dt.weekday(),
                    int(dt.weekday() >= 5),  # is_weekend
                    dt.day,
                    dt.month,
                    (dt.hour >= 9 and dt.hour <= 17),  # business_hours
                    int(dt.hour >= 22 or dt.hour <= 6)  # night_shift
                ]
        except:
            pass
        return [0, 0, 0, 0, 0, 0, 0]
    
    def _extract_message_features(self, message: str) -> List[float]:
        """Extract message-based features"""
        message_lower = message.lower()
        
        # Length and complexity features
        length_features = [
            len(message),
            len(message.split()),
            len(set(message.split())),  # unique words
            message.count('\n'),  # multiline indicator
        ]
        
        # Keyword presence features
        error_keywords = ['error', 'fail', 'exception', 'timeout', 'crash', 'abort']
        critical_keywords = ['critical', 'fatal', 'panic', 'emergency', 'disaster']
        performance_keywords = ['slow', 'latency', 'delay', 'bottleneck', 'hung']
        security_keywords = ['unauthorized', 'breach', 'attack', 'malicious', 'exploit']
        
        keyword_features = [
            sum(1 for kw in error_keywords if kw in message_lower),
            sum(1 for kw in critical_keywords if kw in message_lower),
            sum(1 for kw in performance_keywords if kw in message_lower),
            sum(1 for kw in security_keywords if kw in message_lower),
        ]
        
        # Pattern features
        pattern_features = [
            len(re.findall(r'\d+', message)),  # numeric values
            len(re.findall(r'[A-Z]{2,}', message)),  # uppercase words
            len(re.findall(r'[a-f0-9]{8,}', message)),  # hex patterns
            message.count('null'),
            message.count('NaN'),
            len(re.findall(r'Exception|Error$', message)),  # exception endings
        ]
        
        return length_features + keyword_features + pattern_features
    
    def _extract_service_features(self, service: str, level: str) -> List[float]:
        """Extract service and level features"""
        level_mapping = {
            'DEBUG': 1, 'INFO': 2, 'WARN': 3, 'WARNING': 3,
            'ERROR': 4, 'CRITICAL': 5, 'FATAL': 5
        }
        
        service_hash = hash(service) % 1000  # Service identifier
        level_numeric = level_mapping.get(level.upper(), 0)
        
        return [service_hash, level_numeric, len(service)]
    
    def _extract_network_features(self, message: str) -> List[float]:
        """Extract network and system related features"""
        # IP address patterns
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        port_pattern = r':\d{1,5}\b'
        url_pattern = r'https?://[^\s]+'
        
        return [
            len(re.findall(ip_pattern, message)),
            len(re.findall(port_pattern, message)),
            len(re.findall(url_pattern, message)),
            int('timeout' in message.lower()),
            int('connection' in message.lower()),
        ]
    
    def fit(self, log_data: List[Dict[str, Any]]) -> 'AdvancedAnomalyDetector':
        """Fit multiple anomaly detection models with ensemble approach"""
        if len(log_data) < 10:
            print("Warning: Insufficient data for training. Need at least 10 samples.")
            return self
        
        try:
            # Extract comprehensive features
            features = self.extract_deep_features(log_data)
            
            # Handle different scaling approaches
            robust_scaled = self.robust_scaler.fit_transform(features)
            standard_scaled = self.standard_scaler.fit_transform(features)
            
            # Fit ensemble of models
            models_to_fit = [
                ('isolation_forest', self.isolation_forest, robust_scaled),
                ('one_class_svm', self.one_class_svm, standard_scaled),
                ('dbscan', self.dbscan, robust_scaled),
                ('kmeans', self.kmeans, standard_scaled)
            ]
            
            for model_name, model, scaled_data in models_to_fit:
                try:
                    if model_name == 'kmeans':
                        model.fit(scaled_data)
                        # Calculate distances for anomaly scoring
                        distances = model.transform(scaled_data)
                        self.kmeans_threshold = np.percentile(distances.min(axis=1), 95)
                    else:
                        model.fit(scaled_data)
                    
                    self.model_performance[model_name] = 'fitted'
                    print(f"Successfully fitted {model_name}")
                except Exception as e:
                    print(f"Failed to fit {model_name}: {e}")
                    self.model_performance[model_name] = f'failed: {e}'
            
            # Train adaptive thresholds
            self._update_adaptive_thresholds(log_data)
            
            self.is_fitted = True
            print(f"Advanced anomaly detector fitted with {len(log_data)} samples")
            
        except Exception as e:
            print(f"Error in fitting models: {e}")
            
        return self
    
    def _update_adaptive_thresholds(self, log_data: List[Dict[str, Any]]):
        """Update adaptive thresholds based on historical data"""
        # Calculate baseline metrics
        error_levels = [entry.get('level', '') for entry in log_data if isinstance(entry, dict)]
        error_counts = Counter(error_levels)
        
        total_entries = len(log_data)
        error_ratio = (error_counts.get('ERROR', 0) + error_counts.get('CRITICAL', 0)) / total_entries
        
        # Adaptive threshold based on error ratio
        if error_ratio > 0.1:  # High error environment
            self.adaptive_thresholds['severity_multiplier'] = 0.8
        elif error_ratio > 0.05:  # Medium error environment
            self.adaptive_thresholds['severity_multiplier'] = 1.0
        else:  # Low error environment
            self.adaptive_thresholds['severity_multiplier'] = 1.2
    
    def detect_anomalies_ensemble(self, log_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
        """Detect anomalies using ensemble of ML models"""
        if not self.is_fitted:
            print("Models not fitted. Fitting with provided data...")
            self.fit(log_data)
        
        if not log_data:
            return []
        
        try:
            features = self.extract_deep_features(log_data)
            robust_scaled = self.robust_scaler.transform(features)
            standard_scaled = self.standard_scaler.transform(features)
            
            anomaly_scores = np.zeros(len(log_data))
            model_predictions = {}
            
            # Isolation Forest
            if 'isolation_forest' in self.model_performance:
                try:
                    iso_predictions = self.isolation_forest.predict(robust_scaled)
                    iso_scores = self.isolation_forest.score_samples(robust_scaled)
                    model_predictions['isolation_forest'] = (iso_predictions, iso_scores)
                    
                    # Add to ensemble score
                    anomaly_scores += np.where(iso_predictions == -1, abs(iso_scores), 0) * 0.3
                except Exception as e:
                    print(f"Isolation Forest prediction failed: {e}")
            
            # One-Class SVM
            if 'one_class_svm' in self.model_performance:
                try:
                    svm_predictions = self.one_class_svm.predict(standard_scaled)
                    svm_scores = self.one_class_svm.score_samples(standard_scaled)
                    model_predictions['one_class_svm'] = (svm_predictions, svm_scores)
                    
                    # Add to ensemble score
                    anomaly_scores += np.where(svm_predictions == -1, abs(svm_scores), 0) * 0.25
                except Exception as e:
                    print(f"One-Class SVM prediction failed: {e}")
            
            # DBSCAN clustering
            if 'dbscan' in self.model_performance:
                try:
                    cluster_labels = self.dbscan.fit_predict(robust_scaled)
                    model_predictions['dbscan'] = cluster_labels
                    
                    # Outliers have label -1
                    anomaly_scores += np.where(cluster_labels == -1, 0.5, 0) * 0.2
                except Exception as e:
                    print(f"DBSCAN prediction failed: {e}")
            
            # K-means distance-based detection
            if 'kmeans' in self.model_performance:
                try:
                    distances = self.kmeans.transform(standard_scaled)
                    min_distances = distances.min(axis=1)
                    outlier_mask = min_distances > self.kmeans_threshold
                    model_predictions['kmeans'] = (outlier_mask, min_distances)
                    
                    # Add to ensemble score
                    anomaly_scores += np.where(outlier_mask, min_distances / self.kmeans_threshold, 0) * 0.25
                except Exception as e:
                    print(f"K-means prediction failed: {e}")
            
            # Convert scores to anomaly results
            anomaly_results = []
            score_threshold = np.percentile(anomaly_scores, 95)  # Top 5% as anomalies
            
            for i, (log_entry, score) in enumerate(zip(log_data, anomaly_scores)):
                if score > score_threshold:
                    severity = self._calculate_severity(score, anomaly_scores, log_entry)
                    confidence = min(score * 100, 100)
                    
                    # Generate explanation
                    explanation = self._generate_explanation(log_entry, model_predictions, i)
                    
                    anomaly_results.append(AnomalyResult(
                        index=i,
                        type='ensemble_ml_anomaly',
                        severity=severity,
                        confidence=confidence,
                        data=log_entry,
                        detection_method='ensemble_ml',
                        risk_score=score,
                        explanation=explanation
                    ))
            
            return anomaly_results
            
        except Exception as e:
            print(f"Ensemble detection failed: {e}")
            return []
    
    def _calculate_severity(self, score: float, all_scores: np.ndarray, log_entry: Dict[str, Any]) -> str:
        """Calculate severity based on score and log content"""
        score_percentile = np.percentile(all_scores, 95)
        
        # Base severity on score
        if score > np.percentile(all_scores, 99):
            base_severity = 'CRITICAL'
        elif score > np.percentile(all_scores, 97):
            base_severity = 'HIGH'
        elif score > score_percentile:
            base_severity = 'MEDIUM'
        else:
            base_severity = 'LOW'
        
        # Adjust based on log content
        if isinstance(log_entry, dict):
            level = log_entry.get('level', '').upper()
            message = log_entry.get('message', '').lower()
            
            if level in ['CRITICAL', 'FATAL'] or any(word in message for word in ['critical', 'fatal', 'panic']):
                return 'CRITICAL'
            elif level == 'ERROR' or any(word in message for word in ['error', 'fail', 'exception']):
                return max(base_severity, 'HIGH', key=lambda x: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].index(x))
        
        return base_severity
    
    def _generate_explanation(self, log_entry: Dict[str, Any], model_predictions: Dict, index: int) -> str:
        """Generate human-readable explanation for anomaly detection"""
        explanations = []
        
        # Check each model's contribution
        for model_name, predictions in model_predictions.items():
            if model_name == 'isolation_forest':
                pred, scores = predictions
                if pred[index] == -1:
                    explanations.append(f"Isolation Forest detected unusual feature combination")
            
            elif model_name == 'one_class_svm':
                pred, scores = predictions
                if pred[index] == -1:
                    explanations.append(f"SVM detected deviation from normal patterns")
            
            elif model_name == 'dbscan':
                if predictions[index] == -1:
                    explanations.append(f"DBSCAN identified as outlier cluster")
            
            elif model_name == 'kmeans':
                outlier_mask, distances = predictions
                if outlier_mask[index]:
                    explanations.append(f"K-means detected high distance from cluster centers")
        
        # Add content-based explanation
        if isinstance(log_entry, dict):
            message = log_entry.get('message', '')
            if any(word in message.lower() for word in ['error', 'fail', 'exception']):
                explanations.append("Contains error-related keywords")
            if any(word in message.lower() for word in ['critical', 'fatal', 'panic']):
                explanations.append("Contains critical severity indicators")
        
        return "; ".join(explanations) if explanations else "Multiple models detected anomalous patterns"

class EnhancedLSTMAnomalyDetector(nn.Module):
    """Enhanced LSTM with attention mechanism for sequential log pattern detection"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, num_layers: int = 3, dropout: float = 0.2):
        super(EnhancedLSTMAnomalyDetector, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM layers with dropout
        self.lstm = nn.LSTM(
            input_size, hidden_size, num_layers,
            batch_first=True, dropout=dropout, bidirectional=True
        )
        
        # Attention mechanism
        self.attention = nn.Linear(hidden_size * 2, 1)
        
        # Classification layers
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
        # Anomaly reconstruction for unsupervised learning
        self.reconstructor = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, input_size),
            nn.Tanh()
        )
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # LSTM forward pass
        lstm_out, _ = self.lstm(x)
        
        # Attention mechanism
        attention_weights = torch.softmax(self.attention(lstm_out), dim=1)
        attended_output = torch.sum(lstm_out * attention_weights, dim=1)
        
        # Classification output
        classification = self.classifier(attended_output)
        
        # Reconstruction output
        reconstruction = self.reconstructor(attended_output)
        
        return classification, reconstruction
    
    def detect_anomalies(self, sequences: torch.Tensor, threshold: float = 0.5) -> np.ndarray:
        """Detect anomalies in sequences"""
        self.eval()
        with torch.no_grad():
            classification, reconstruction = self.forward(sequences)
            
            # Calculate reconstruction error
            reconstruction_error = torch.mean((sequences.mean(dim=1) - reconstruction) ** 2, dim=1)
            
            # Combine classification and reconstruction scores
            anomaly_scores = classification.squeeze() + reconstruction_error
            
            return anomaly_scores.numpy()

class GenerativeAIAnalyzer:
    """Advanced Generative AI-powered log analysis with sophisticated pattern recognition"""
    
    def __init__(self):
        self.pattern_templates = {
            'error_patterns': [
                "Connection error pattern: {error_type} affecting {service} with {frequency} occurrences",
                "Database error pattern: {error_type} in {operation} causing {impact}",
                "Authentication error pattern: {error_type} for {user_context} from {source}",
                "Performance error pattern: {error_type} with {metric_value} exceeding threshold",
                "Memory error pattern: {error_type} consuming {memory_usage} in {component}",
                "Network error pattern: {error_type} on {network_component} affecting {services}"
            ],
            'anomaly_explanations': [
                "Unusual error frequency detected in {time_window}: {frequency_change}% increase",
                "Abnormal error pattern: {pattern_description} deviating from baseline",
                "System behavior deviation: {deviation_type} in {affected_components}",
                "Cascade failure detected: {failure_chain} across {service_count} services",
                "Performance degradation: {performance_metrics} below normal thresholds",
                "Security anomaly: {security_pattern} detected in {time_range}"
            ],
            'recommendation_templates': [
                "Immediate action: {action_type} required for {component} due to {reason}",
                "Preventive measure: {prevention_type} should be implemented to avoid {risk}",
                "Monitoring enhancement: {monitoring_type} needs improvement for {coverage}",
                "Capacity planning: {resource_type} scaling required based on {trend_analysis}"
            ]
        }
        
        # Advanced pattern recognition
        self.semantic_patterns = {
            'performance_degradation': [
                r'response.*time.*([5-9]\d{3,}|\d{5,})',
                r'slow.*query.*(\d+\.?\d*)\s*sec',
                r'timeout.*(\d+)\s*ms',
                r'latency.*(\d+\.?\d*)\s*(ms|sec)'
            ],
            'security_threats': [
                r'failed.*login.*attempts.*(\d+)',
                r'unauthorized.*access.*(\w+)',
                r'suspicious.*activity.*(\w+)',
                r'malware.*detected.*(\w+)'
            ],
            'system_failures': [
                r'out.*of.*memory.*(\d+)',
                r'disk.*full.*(\d+%)',
                r'service.*unavailable.*(\w+)',
                r'connection.*refused.*(\w+)'
            ],
            'data_integrity': [
                r'checksum.*mismatch.*(\w+)',
                r'data.*corruption.*(\w+)',
                r'integrity.*violation.*(\w+)',
                r'invalid.*format.*(\w+)'
            ]
        }
        
        # AI-powered insight generation
        self.insight_generators = {
            'trend_analysis': self._generate_trend_insights,
            'correlation_analysis': self._generate_correlation_insights,
            'predictive_analysis': self._generate_predictive_insights,
            'impact_analysis': self._generate_impact_insights
        }
        
        # Context-aware recommendations
        self.recommendation_engine = RecommendationEngine()
        
    def generate_comprehensive_insights(self, anomaly_data: List[AnomalyResult], 
                                      error_data: List[Dict[str, Any]], 
                                      historical_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Generate comprehensive AI-powered insights"""
        
        insights = {
            'error_analysis': self._analyze_error_patterns(error_data),
            'anomaly_analysis': self._analyze_anomaly_patterns(anomaly_data),
            'temporal_analysis': self._analyze_temporal_patterns(anomaly_data + error_data),
            'service_impact': self._analyze_service_impact(anomaly_data + error_data),
            'root_cause_hypothesis': self._generate_root_cause_hypotheses(anomaly_data, error_data),
            'predictive_warnings': self._generate_predictive_warnings(anomaly_data, error_data),
            'recommendations': self._generate_advanced_recommendations(anomaly_data, error_data)
        }
        
        # Add historical context if available
        if historical_data:
            insights['historical_comparison'] = self._compare_with_historical_data(
                anomaly_data, error_data, historical_data
            )
        
        return insights
    
    def _analyze_error_patterns(self, error_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Deep analysis of error patterns using AI techniques"""
        
        # Extract and categorize errors
        error_categories = defaultdict(list)
        error_frequency = defaultdict(int)
        service_errors = defaultdict(list)
        
        for error in error_data:
            if isinstance(error, dict):
                message = error.get('message', '').lower()
                service = error.get('service', 'unknown')
                timestamp = error.get('timestamp', '')
                
                # Semantic categorization
                category = self._categorize_error_semantically(message)
                error_categories[category].append(error)
                error_frequency[category] += 1
                service_errors[service].append(category)
        
        # Generate insights
        primary_error_category = max(error_frequency.items(), key=lambda x: x[1])
        most_affected_service = max(service_errors.items(), key=lambda x: len(x[1]))
        
        return {
            'total_errors': len(error_data),
            'error_categories': dict(error_frequency),
            'primary_error_category': primary_error_category[0],
            'primary_error_count': primary_error_category[1],
            'most_affected_service': most_affected_service[0],
            'service_error_diversity': len(set(most_affected_service[1])),
            'error_distribution': self._calculate_error_distribution(error_categories),
            'severity_analysis': self._analyze_error_severity(error_data)
        }
    
    def _categorize_error_semantically(self, message: str) -> str:
        """Categorize errors using semantic analysis"""
        for category, patterns in self.semantic_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    return category
        
        # Fallback categorization
        if any(word in message for word in ['connection', 'network', 'timeout']):
            return 'connectivity_issues'
        elif any(word in message for word in ['database', 'sql', 'query']):
            return 'database_issues'
        elif any(word in message for word in ['auth', 'login', 'permission']):
            return 'authentication_issues'
        else:
            return 'general_errors'
    
    def _analyze_anomaly_patterns(self, anomaly_data: List[AnomalyResult]) -> Dict[str, Any]:
        """Analyze anomaly patterns for insights"""
        
        if not anomaly_data:
            return {'message': 'No anomalies to analyze'}
        
        # Severity distribution
        severity_counts = Counter([a.severity for a in anomaly_data])
        
        # Detection method analysis
        detection_methods = Counter([a.detection_method for a in anomaly_data])
        
        # Risk score analysis
        risk_scores = [a.risk_score for a in anomaly_data if a.risk_score > 0]
        
        # Confidence analysis
        confidence_scores = [a.confidence for a in anomaly_data]
        
        return {
            'total_anomalies': len(anomaly_data),
            'severity_distribution': dict(severity_counts),
            'detection_methods': dict(detection_methods),
            'avg_risk_score': np.mean(risk_scores) if risk_scores else 0,
            'max_risk_score': max(risk_scores) if risk_scores else 0,
            'avg_confidence': np.mean(confidence_scores) if confidence_scores else 0,
            'high_risk_anomalies': len([a for a in anomaly_data if a.risk_score > 0.7]),
            'critical_anomalies': len([a for a in anomaly_data if a.severity == 'CRITICAL'])
        }
    
    def _analyze_temporal_patterns(self, combined_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze temporal patterns in data"""
        
        # Extract timestamps
        timestamps = []
        for item in combined_data:
            if isinstance(item, dict) and 'timestamp' in item:
                try:
                    dt = datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00'))
                    timestamps.append(dt)
                except:
                    pass
            elif hasattr(item, 'timestamp') and item.timestamp:
                try:
                    dt = datetime.fromisoformat(item.timestamp.replace('Z', '+00:00'))
                    timestamps.append(dt)
                except:
                    pass
        
        if not timestamps:
            return {'message': 'No valid timestamps found'}
        
        # Analyze patterns
        hourly_counts = defaultdict(int)
        daily_counts = defaultdict(int)
        
        for ts in timestamps:
            hourly_counts[ts.hour] += 1
            daily_counts[ts.weekday()] += 1
        
        # Find peak times
        peak_hour = max(hourly_counts.items(), key=lambda x: x[1])
        peak_day = max(daily_counts.items(), key=lambda x: x[1])
        
        return {
            'time_range': f"{min(timestamps)} to {max(timestamps)}",
            'peak_hour': f"{peak_hour[0]:02d}:00 ({peak_hour[1]} events)",
            'peak_day': f"{['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][peak_day[0]]} ({peak_day[1]} events)",
            'hourly_distribution': dict(hourly_counts),
            'daily_distribution': dict(daily_counts),
            'total_events': len(timestamps)
        }
    
    def _generate_root_cause_hypotheses(self, anomaly_data: List[AnomalyResult], 
                                       error_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate AI-powered root cause hypotheses"""
        
        hypotheses = []
        
        # Analyze error-anomaly correlation
        error_types = Counter()
        anomaly_types = Counter()
        
        for error in error_data:
            if isinstance(error, dict):
                message = error.get('message', '').lower()
                error_type = self._categorize_error_semantically(message)
                error_types[error_type] += 1
        
        for anomaly in anomaly_data:
            anomaly_types[anomaly.type] += 1
        
        # Generate hypotheses based on patterns
        if error_types.get('performance_degradation', 0) > 5:
            hypotheses.append({
                'hypothesis': 'Performance bottleneck causing cascading failures',
                'confidence': 0.85,
                'evidence': f"Detected {error_types['performance_degradation']} performance-related errors",
                'investigation_steps': [
                    'Check CPU and memory utilization',
                    'Analyze database query performance',
                    'Review recent code deployments',
                    'Examine network latency patterns'
                ]
            })
        
        if error_types.get('security_threats', 0) > 3:
            hypotheses.append({
                'hypothesis': 'Security incident affecting system stability',
                'confidence': 0.9,
                'evidence': f"Detected {error_types['security_threats']} security-related events",
                'investigation_steps': [
                    'Review security logs and access patterns',
                    'Check for unauthorized access attempts',
                    'Analyze firewall and intrusion detection logs',
                    'Validate user authentication patterns'
                ]
            })
        
        return hypotheses
    
    def _generate_predictive_warnings(self, anomaly_data: List[AnomalyResult], 
                                    error_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate predictive warnings based on current patterns"""
        
        warnings = []
        
        # Trend analysis
        high_risk_count = len([a for a in anomaly_data if a.risk_score > 0.7])
        total_anomalies = len(anomaly_data)
        
        if high_risk_count > total_anomalies * 0.3:
            warnings.append({
                'warning': 'High risk of system instability',
                'probability': 0.75,
                'timeframe': '1-2 hours',
                'reasoning': f'{high_risk_count}/{total_anomalies} anomalies are high risk',
                'preventive_actions': [
                    'Increase monitoring frequency',
                    'Prepare incident response team',
                    'Review system capacity',
                    'Check backup systems'
                ]
            })
        
        # Error escalation prediction
        error_rate = len(error_data) / max(1, len(anomaly_data) + len(error_data))
        if error_rate > 0.4:
            warnings.append({
                'warning': 'Potential error escalation',
                'probability': 0.6,
                'timeframe': '30-60 minutes',
                'reasoning': f'Error rate at {error_rate:.1%} indicates potential escalation',
                'preventive_actions': [
                    'Scale up critical services',
                    'Implement circuit breakers',
                    'Prepare rollback procedures',
                    'Alert key personnel'
                ]
            })
        
        return warnings
    
    def _generate_advanced_recommendations(self, anomaly_data: List[AnomalyResult], 
                                         error_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate advanced AI-powered recommendations"""
        
        return self.recommendation_engine.generate_recommendations(anomaly_data, error_data)
    
    def _generate_trend_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Generate trend-based insights"""
        insights = []
        
        # Analyze temporal trends
        if len(data) > 10:
            timestamps = []
            for item in data:
                if isinstance(item, dict) and 'timestamp' in item:
                    try:
                        dt = datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00'))
                        timestamps.append(dt)
                    except:
                        pass
            
            if timestamps:
                # Calculate time differences
                time_diffs = [(timestamps[i+1] - timestamps[i]).total_seconds() 
                             for i in range(len(timestamps)-1)]
                
                if time_diffs:
                    avg_interval = sum(time_diffs) / len(time_diffs)
                    insights.append(f"Average event interval: {avg_interval:.1f} seconds")
                    
                    # Detect bursts
                    short_intervals = [diff for diff in time_diffs if diff < avg_interval * 0.5]
                    if len(short_intervals) > len(time_diffs) * 0.3:
                        insights.append("Event burst pattern detected")
        
        return insights
    
    def _generate_correlation_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Generate correlation-based insights"""
        insights = []
        
        # Service correlation analysis
        service_errors = defaultdict(int)
        for item in data:
            if isinstance(item, dict):
                service = item.get('service', 'unknown')
                level = item.get('level', '').upper()
                if level in ['ERROR', 'CRITICAL']:
                    service_errors[service] += 1
        
        if service_errors:
            most_affected = max(service_errors.items(), key=lambda x: x[1])
            insights.append(f"Service '{most_affected[0]}' shows highest error correlation")
            
            # Cross-service impact
            if len(service_errors) > 1:
                insights.append(f"Multi-service impact detected across {len(service_errors)} services")
        
        return insights
    
    def _generate_predictive_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Generate predictive insights"""
        insights = []
        
        # Error escalation prediction
        error_levels = []
        for item in data:
            if isinstance(item, dict):
                level = item.get('level', '').upper()
                if level in ['WARN', 'ERROR', 'CRITICAL']:
                    error_levels.append(level)
        
        if error_levels:
            critical_ratio = error_levels.count('CRITICAL') / len(error_levels)
            error_ratio = error_levels.count('ERROR') / len(error_levels)
            
            if critical_ratio > 0.2:
                insights.append("High probability of system instability")
            elif error_ratio > 0.4:
                insights.append("Moderate risk of error escalation")
        
        return insights
    
    def _generate_impact_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Generate impact analysis insights"""
        insights = []
        
        # Service impact assessment
        services = set()
        error_services = set()
        
        for item in data:
            if isinstance(item, dict):
                service = item.get('service', 'unknown')
                level = item.get('level', '').upper()
                services.add(service)
                if level in ['ERROR', 'CRITICAL']:
                    error_services.add(service)
        
        if services:
            impact_ratio = len(error_services) / len(services)
            if impact_ratio > 0.5:
                insights.append("High impact: majority of services affected")
            elif impact_ratio > 0.3:
                insights.append("Medium impact: multiple services affected")
        
        return insights
    
    def _compare_with_historical_data(self, current_anomalies: List[AnomalyResult], 
                                    current_errors: List[Dict[str, Any]], 
                                    historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compare current data with historical patterns"""
        comparison = {}
        
        # Compare error rates
        current_error_rate = len([e for e in current_errors if e.get('level') == 'ERROR']) / len(current_errors) if current_errors else 0
        historical_error_rate = len([e for e in historical_data if e.get('level') == 'ERROR']) / len(historical_data) if historical_data else 0
        
        if current_error_rate > historical_error_rate * 1.5:
            comparison['error_rate_trend'] = 'significantly_higher'
        elif current_error_rate > historical_error_rate * 1.2:
            comparison['error_rate_trend'] = 'moderately_higher'
        else:
            comparison['error_rate_trend'] = 'normal'
        
        # Compare anomaly counts
        current_anomaly_count = len(current_anomalies)
        # Estimate historical anomaly count (simplified)
        historical_anomaly_count = max(1, len(historical_data) // 20)  # Assume 5% anomaly rate
        
        if current_anomaly_count > historical_anomaly_count * 2:
            comparison['anomaly_trend'] = 'much_higher'
        elif current_anomaly_count > historical_anomaly_count * 1.5:
            comparison['anomaly_trend'] = 'higher'
        else:
            comparison['anomaly_trend'] = 'normal'
        
        return comparison
    
    def _calculate_error_distribution(self, error_categories: Dict[str, List[Dict[str, Any]]]) -> Dict[str, float]:
        """Calculate error distribution percentages"""
        total_errors = sum(len(errors) for errors in error_categories.values())
        
        if total_errors == 0:
            return {}
        
        return {
            category: len(errors) / total_errors * 100 
            for category, errors in error_categories.items()
        }
    
    def _analyze_error_severity(self, error_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze error severity distribution"""
        severity_counts = defaultdict(int)
        
        for error in error_data:
            if isinstance(error, dict):
                level = error.get('level', '').upper()
                severity_counts[level] += 1
        
        total_errors = sum(severity_counts.values())
        
        return {
            'severity_counts': dict(severity_counts),
            'total_errors': total_errors,
            'critical_percentage': (severity_counts.get('CRITICAL', 0) / total_errors * 100) if total_errors > 0 else 0,
            'error_percentage': (severity_counts.get('ERROR', 0) / total_errors * 100) if total_errors > 0 else 0
        }
    
    def _analyze_service_impact(self, combined_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze service impact from combined anomaly and error data"""
        
        service_stats = defaultdict(lambda: {
            'total_events': 0,
            'error_events': 0,
            'critical_events': 0,
            'affected_hosts': set(),
            'error_types': set()
        })
        
        for item in combined_data:
            data_dict = item
            if hasattr(item, 'data'):
                data_dict = item.data
            elif hasattr(item, '__dict__'):
                data_dict = item.__dict__
            
            if isinstance(data_dict, dict):
                service = data_dict.get('service', 'unknown')
                level = data_dict.get('level', '').upper()
                host = data_dict.get('host', 'unknown')
                message = data_dict.get('message', '')
                
                service_stats[service]['total_events'] += 1
                service_stats[service]['affected_hosts'].add(host)
                
                if level in ['ERROR', 'CRITICAL']:
                    service_stats[service]['error_events'] += 1
                    
                    # Categorize error types
                    if 'connection' in message.lower():
                        service_stats[service]['error_types'].add('connection')
                    elif 'memory' in message.lower():
                        service_stats[service]['error_types'].add('memory')
                    elif 'auth' in message.lower():
                        service_stats[service]['error_types'].add('authentication')
                    elif 'performance' in message.lower():
                        service_stats[service]['error_types'].add('performance')
                
                if level == 'CRITICAL':
                    service_stats[service]['critical_events'] += 1
        
        # Calculate impact metrics
        impact_analysis = {}
        
        for service, stats in service_stats.items():
            error_rate = stats['error_events'] / stats['total_events'] if stats['total_events'] > 0 else 0
            critical_rate = stats['critical_events'] / stats['total_events'] if stats['total_events'] > 0 else 0
            
            # Determine impact level
            if critical_rate > 0.1 or error_rate > 0.3:
                impact_level = 'HIGH'
            elif critical_rate > 0.05 or error_rate > 0.15:
                impact_level = 'MEDIUM'
            else:
                impact_level = 'LOW'
            
            impact_analysis[service] = {
                'impact_level': impact_level,
                'error_rate': error_rate,
                'critical_rate': critical_rate,
                'total_events': stats['total_events'],
                'error_events': stats['error_events'],
                'critical_events': stats['critical_events'],
                'affected_hosts': len(stats['affected_hosts']),
                'error_types': list(stats['error_types'])
            }
        
        return impact_analysis

class RecommendationEngine:
    """Advanced recommendation engine for system optimization"""
    
    def __init__(self):
        self.recommendation_patterns = {
            'performance': {
                'triggers': ['slow', 'latency', 'timeout', 'response_time'],
                'recommendations': [
                    'Optimize database queries and indexing',
                    'Implement caching strategies',
                    'Scale up compute resources',
                    'Review and optimize algorithms'
                ]
            },
            'security': {
                'triggers': ['unauthorized', 'breach', 'attack', 'malicious'],
                'recommendations': [
                    'Strengthen authentication mechanisms',
                    'Implement additional security monitoring',
                    'Review and update security policies',
                    'Conduct security audit'
                ]
            },
            'reliability': {
                'triggers': ['failure', 'crash', 'unavailable', 'error'],
                'recommendations': [
                    'Implement circuit breakers',
                    'Add redundancy and failover mechanisms',
                    'Improve error handling and recovery',
                    'Enhance monitoring and alerting'
                ]
            }
        }
    
    def generate_recommendations(self, anomaly_data: List[AnomalyResult], 
                               error_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate context-aware recommendations"""
        
        recommendations = []
        
        # Analyze patterns for recommendations
        pattern_scores = defaultdict(float)
        
        # Score patterns based on anomalies
        for anomaly in anomaly_data:
            message = str(anomaly.data).lower()
            for pattern, config in self.recommendation_patterns.items():
                trigger_score = sum(1 for trigger in config['triggers'] if trigger in message)
                pattern_scores[pattern] += trigger_score * (anomaly.risk_score / 100)
        
        # Score patterns based on errors
        for error in error_data:
            if isinstance(error, dict):
                message = error.get('message', '').lower()
                for pattern, config in self.recommendation_patterns.items():
                    trigger_score = sum(1 for trigger in config['triggers'] if trigger in message)
                    pattern_scores[pattern] += trigger_score * 0.5
        
        # Generate recommendations based on scores
        for pattern, score in pattern_scores.items():
            if score > 1.0:  # Threshold for recommendation
                config = self.recommendation_patterns[pattern]
                recommendations.append({
                    'category': pattern,
                    'priority': 'HIGH' if score > 3.0 else 'MEDIUM',
                    'confidence': min(score / 5.0, 1.0),
                    'recommendations': config['recommendations'],
                    'reasoning': f'Detected {score:.1f} score for {pattern} issues'
                })
        
        return recommendations

def detect_anomalies_advanced(processed_data: List[Dict[str, Any]], 
                            use_ml: bool = True, 
                            use_lstm: bool = False, 
                            use_generative_ai: bool = True,
                            real_time_mode: bool = False) -> Dict[str, Any]:
    """
    Advanced anomaly detection with multiple algorithms and AI insights
    
    Args:
        processed_data: List of processed log entries
        use_ml: Whether to use machine learning models
        use_lstm: Whether to use LSTM neural networks
        use_generative_ai: Whether to use generative AI analysis
        real_time_mode: Whether to operate in real-time mode
    
    Returns:
        Comprehensive anomaly detection results with AI insights
    """
    if not processed_data:
        return {'anomalies': [], 'summary': 'No data to analyze'}
    
    print(f"Starting advanced anomaly detection on {len(processed_data)} entries...")
    
    # Initialize components
    advanced_detector = AdvancedAnomalyDetector()
    ai_analyzer = GenerativeAIAnalyzer()
    
    all_anomalies = []
    
    # Real-time buffer management
    if real_time_mode:
        for entry in processed_data:
            advanced_detector.real_time_buffer.add_entry(entry)
    
    # 1. Enhanced ML-based detection
    if use_ml and len(processed_data) > 10:
        print("Running ensemble ML detection...")
        try:
            ml_anomalies = advanced_detector.detect_anomalies_ensemble(processed_data)
            all_anomalies.extend(ml_anomalies)
            print(f"ML detection found {len(ml_anomalies)} anomalies")
        except Exception as e:
            print(f"ML detection failed: {e}")
    
    # 2. Enhanced pattern-based detection
    print("Running enhanced pattern detection...")
    pattern_anomalies = detect_enhanced_patterns(processed_data)
    all_anomalies.extend(pattern_anomalies)
    print(f"Pattern detection found {len(pattern_anomalies)} anomalies")
    
    # 3. LSTM-based sequential detection
    if use_lstm and len(processed_data) > 50:
        print("Running LSTM sequential detection...")
        try:
            lstm_anomalies = detect_lstm_anomalies(processed_data)
            all_anomalies.extend(lstm_anomalies)
            print(f"LSTM detection found {len(lstm_anomalies)} anomalies")
        except Exception as e:
            print(f"LSTM detection failed: {e}")
    
    # 4. Time-series anomaly detection
    print("Running time-series analysis...")
    time_anomalies = detect_time_based_anomalies_advanced(processed_data)
    all_anomalies.extend(time_anomalies)
    print(f"Time-series detection found {len(time_anomalies)} anomalies")
    
    # 5. Correlation-based detection
    print("Running correlation analysis...")
    correlation_anomalies = detect_correlation_anomalies(processed_data)
    all_anomalies.extend(correlation_anomalies)
    print(f"Correlation detection found {len(correlation_anomalies)} anomalies")
    
    # Remove duplicates and merge similar anomalies
    unique_anomalies = deduplicate_anomalies(all_anomalies)
    
    # Generate comprehensive AI insights
    ai_insights = {}
    ai_recommendations = []
    
    if use_generative_ai:
        print("Generating AI insights...")
        try:
            # Extract error data from anomalies
            error_data = []
            for anomaly in unique_anomalies:
                if isinstance(anomaly, AnomalyResult):
                    error_data.append(anomaly.data)
                elif isinstance(anomaly, dict):
                    error_data.append(anomaly.get('data', {}))
            
            ai_insights = ai_analyzer.generate_comprehensive_insights(
                unique_anomalies, error_data
            )
            ai_recommendations = ai_insights.get('recommendations', [])
            print(f"Generated {len(ai_recommendations)} AI recommendations")
        except Exception as e:
            print(f"AI analysis failed: {e}")
    
    # Calculate comprehensive statistics
    severity_counts = Counter()
    detection_methods = Counter()
    confidence_scores = []
    risk_scores = []
    
    for anomaly in unique_anomalies:
        if isinstance(anomaly, AnomalyResult):
            severity_counts[anomaly.severity] += 1
            detection_methods[anomaly.detection_method] += 1
            confidence_scores.append(anomaly.confidence)
            if anomaly.risk_score > 0:
                risk_scores.append(anomaly.risk_score)
        elif isinstance(anomaly, dict):
            severity_counts[anomaly.get('severity', 'UNKNOWN')] += 1
            detection_methods[anomaly.get('detection_method', 'unknown')] += 1
            if 'confidence' in anomaly:
                confidence_scores.append(anomaly['confidence'])
    
    # Performance metrics
    high_severity_count = severity_counts.get('HIGH', 0) + severity_counts.get('CRITICAL', 0)
    critical_count = severity_counts.get('CRITICAL', 0)
    
    return {
        'anomalies': [
            anomaly.__dict__ if isinstance(anomaly, AnomalyResult) else anomaly 
            for anomaly in unique_anomalies
        ],
        'count': len(unique_anomalies),
        'severity_distribution': dict(severity_counts),
        'detection_methods': dict(detection_methods),
        'statistics': {
            'avg_confidence': np.mean(confidence_scores) if confidence_scores else 0,
            'max_confidence': max(confidence_scores) if confidence_scores else 0,
            'avg_risk_score': np.mean(risk_scores) if risk_scores else 0,
            'max_risk_score': max(risk_scores) if risk_scores else 0,
            'high_severity_count': high_severity_count,
            'critical_count': critical_count,
            'detection_coverage': len(detection_methods)
        },
        'ai_insights': ai_insights,
        'ai_recommendations': ai_recommendations,
        'summary': f'Advanced detection found {len(unique_anomalies)} anomalies using {len(detection_methods)} methods',
        'performance_metrics': {
            'total_processed': len(processed_data),
            'anomaly_rate': len(unique_anomalies) / len(processed_data) * 100,
            'critical_rate': critical_count / len(processed_data) * 100 if processed_data else 0
        }
    }

def detect_enhanced_patterns(processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
    """Enhanced pattern-based anomaly detection"""
    
    # Advanced suspicious patterns
    enhanced_patterns = {
        'cascade_failure': {
            'pattern': re.compile(r'(ERROR.*\n){5,}', re.MULTILINE),
            'severity': 'CRITICAL',
            'description': 'Cascade failure pattern detected'
        },
        'memory_exhaustion': {
            'pattern': re.compile(r'(OutOfMemoryError|MemoryError|heap.*exhausted|OOM)', re.IGNORECASE),
            'severity': 'CRITICAL',
            'description': 'Memory exhaustion detected'
        },
        'security_breach': {
            'pattern': re.compile(r'(unauthorized|breach|attack|malicious|injection|exploit|intrusion)', re.IGNORECASE),
            'severity': 'CRITICAL',
            'description': 'Security threat detected'
        },
        'performance_degradation': {
            'pattern': re.compile(r'(slow|timeout|latency.*[5-9]\d{3,}|response.*time.*[5-9]\d{3,})', re.IGNORECASE),
            'severity': 'HIGH',
            'description': 'Performance degradation detected'
        },
        'data_corruption': {
            'pattern': re.compile(r'(corrupt|invalid.*data|checksum.*fail|integrity.*violation)', re.IGNORECASE),
            'severity': 'HIGH',
            'description': 'Data integrity issue detected'
        },
        'resource_exhaustion': {
            'pattern': re.compile(r'(disk.*full|no.*space|quota.*exceeded|resource.*limit)', re.IGNORECASE),
            'severity': 'HIGH',
            'description': 'Resource exhaustion detected'
        },
        'network_anomaly': {
            'pattern': re.compile(r'(connection.*refused|network.*unreachable|dns.*fail|packet.*loss)', re.IGNORECASE),
            'severity': 'MEDIUM',
            'description': 'Network connectivity issue detected'
        }
    }
    
    anomalies = []
    
    for i, log_entry in enumerate(processed_data):
        if isinstance(log_entry, dict):
            # Create searchable text
            searchable_text = ' '.join([
                str(log_entry.get('timestamp', '')),
                str(log_entry.get('level', '')),
                str(log_entry.get('message', '')),
                str(log_entry.get('service', ''))
            ])
            
            # Check each pattern
            for pattern_name, pattern_info in enhanced_patterns.items():
                if pattern_info['pattern'].search(searchable_text):
                    anomalies.append(AnomalyResult(
                        index=i,
                        type=f'pattern_{pattern_name}',
                        severity=pattern_info['severity'],
                        confidence=90.0,
                        data=log_entry,
                        detection_method='enhanced_pattern_matching',
                        pattern_matched=pattern_name,
                        explanation=pattern_info['description']
                    ))
    
    return anomalies

def detect_lstm_anomalies(processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
    """LSTM-based sequential anomaly detection"""
    
    try:
        # Prepare sequential data
        sequences = prepare_sequential_data(processed_data)
        
        if len(sequences) < 10:
            return []
        
        # Initialize LSTM model
        input_size = sequences.shape[2] if len(sequences.shape) > 2 else 10
        lstm_model = EnhancedLSTMAnomalyDetector(input_size)
        
        # Convert to torch tensors
        sequences_tensor = torch.FloatTensor(sequences)
        
        # Detect anomalies
        anomaly_scores = lstm_model.detect_anomalies(sequences_tensor)
        
        # Convert to anomaly results
        anomalies = []
        threshold = np.percentile(anomaly_scores, 95)
        
        for i, score in enumerate(anomaly_scores):
            if score > threshold and i < len(processed_data):
                severity = 'CRITICAL' if score > np.percentile(anomaly_scores, 98) else 'HIGH'
                anomalies.append(AnomalyResult(
                    index=i,
                    type='lstm_sequential_anomaly',
                    severity=severity,
                    confidence=min(score * 100, 100),
                    data=processed_data[i],
                    detection_method='lstm_neural_network',
                    risk_score=score,
                    explanation=f'LSTM detected sequential anomaly with score {score:.3f}'
                ))
        
        return anomalies
        
    except Exception as e:
        print(f"LSTM detection failed: {e}")
        return []

def prepare_sequential_data(processed_data: List[Dict[str, Any]], sequence_length: int = 10) -> np.ndarray:
    """Prepare data for LSTM processing"""
    
    # Extract numerical features
    features = []
    for entry in processed_data:
        if isinstance(entry, dict):
            # Extract basic features
            timestamp_features = extract_timestamp_features(entry.get('timestamp', ''))
            message_features = extract_message_numerical_features(entry.get('message', ''))
            level_features = extract_level_features(entry.get('level', ''))
            
            combined_features = timestamp_features + message_features + level_features
            features.append(combined_features)
    
    if not features:
        return np.array([])
    
    # Pad or truncate to fixed size
    max_features = 10
    padded_features = []
    for feature_vector in features:
        if len(feature_vector) < max_features:
            padded_features.append(feature_vector + [0] * (max_features - len(feature_vector)))
        else:
            padded_features.append(feature_vector[:max_features])
    
    features_array = np.array(padded_features)
    
    # Create sequences
    sequences = []
    for i in range(len(features_array) - sequence_length + 1):
        sequences.append(features_array[i:i + sequence_length])
    
    return np.array(sequences)

def extract_timestamp_features(timestamp: str) -> List[float]:
    """Extract numerical features from timestamp"""
    try:
        if timestamp:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return [dt.hour, dt.weekday(), dt.day, dt.month]
    except:
        pass
    return [0, 0, 0, 0]

def extract_message_numerical_features(message: str) -> List[float]:
    """Extract numerical features from message"""
    return [
        len(message),
        len(message.split()),
        message.count('error'),
        message.count('warning'),
        len(re.findall(r'\d+', message))
    ]

def extract_level_features(level: str) -> List[float]:
    """Extract numerical features from log level"""
    level_mapping = {'DEBUG': 1, 'INFO': 2, 'WARN': 3, 'ERROR': 4, 'CRITICAL': 5}
    return [level_mapping.get(level.upper(), 0)]

def detect_time_based_anomalies_advanced(processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
    """Advanced time-based anomaly detection"""
    
    # Group by time windows
    time_windows = defaultdict(list)
    
    for i, entry in enumerate(processed_data):
        if isinstance(entry, dict) and 'timestamp' in entry:
            try:
                dt = datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00'))
                # 5-minute windows
                window_key = dt.replace(minute=dt.minute - dt.minute % 5, second=0, microsecond=0)
                time_windows[window_key].append((i, entry))
            except:
                pass
    
    anomalies = []
    
    # Analyze each time window
    window_sizes = [len(entries) for entries in time_windows.values()]
    if window_sizes:
        mean_size = np.mean(window_sizes)
        std_size = np.std(window_sizes)
        
        for window_time, entries in time_windows.items():
            # Check for spikes
            if len(entries) > mean_size + 2 * std_size:
                # This is a spike
                for i, entry in entries:
                    anomalies.append(AnomalyResult(
                        index=i,
                        type='time_window_spike',
                        severity='HIGH',
                        confidence=85.0,
                        data=entry,
                        detection_method='time_series_analysis',
                        timestamp=window_time.isoformat(),
                        explanation=f'Activity spike detected: {len(entries)} events in 5-minute window'
                    ))
    
    return anomalies

def detect_correlation_anomalies(processed_data: List[Dict[str, Any]]) -> List[AnomalyResult]:
    """Detect anomalies based on service and error correlations"""
    
    anomalies = []
    
    # Build correlation matrix
    service_errors = defaultdict(list)
    error_patterns = defaultdict(list)
    
    for i, entry in enumerate(processed_data):
        if isinstance(entry, dict):
            service = entry.get('service', 'unknown')
            message = entry.get('message', '').lower()
            level = entry.get('level', '').upper()
            
            service_errors[service].append((i, level, message))
            
            # Extract error patterns
            if level == 'ERROR':
                error_patterns[service].append((i, message))
    
    # Detect anomalous correlations
    for service, errors in service_errors.items():
        if len(errors) > 10:  # Sufficient data
            error_levels = [error[1] for error in errors]
            error_count = len([e for e in error_levels if e == 'ERROR'])
            
            # High error rate for this service
            if error_count > len(errors) * 0.5:
                for i, level, message in errors:
                    if level == 'ERROR':
                        anomalies.append(AnomalyResult(
                            index=i,
                            type='service_error_correlation',
                            severity='HIGH',
                            confidence=75.0,
                            data=processed_data[i],
                            detection_method='correlation_analysis',
                            explanation=f'Service {service} has unusually high error rate: {error_count}/{len(errors)}'
                        ))
    
    return anomalies

def deduplicate_anomalies(anomalies: List[AnomalyResult]) -> List[AnomalyResult]:
    """Remove duplicate anomalies and merge similar ones"""
    
    if not anomalies:
        return []
    
    # Group by index to avoid multiple detections on same log entry
    index_groups = defaultdict(list)
    
    for anomaly in anomalies:
        if isinstance(anomaly, AnomalyResult):
            index_groups[anomaly.index].append(anomaly)
        elif isinstance(anomaly, dict):
            index_groups[anomaly.get('index', -1)].append(anomaly)
    
    # For each index, keep the highest severity anomaly
    unique_anomalies = []
    
    for index, group in index_groups.items():
        if len(group) == 1:
            unique_anomalies.append(group[0])
        else:
            # Find highest severity
            severity_order = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
            
            best_anomaly = group[0]
            for anomaly in group[1:]:
                if isinstance(anomaly, AnomalyResult):
                    current_severity = anomaly.severity
                    best_severity = best_anomaly.severity if isinstance(best_anomaly, AnomalyResult) else best_anomaly.get('severity', 'LOW')
                else:
                    current_severity = anomaly.get('severity', 'LOW')
                    best_severity = best_anomaly.severity if isinstance(best_anomaly, AnomalyResult) else best_anomaly.get('severity', 'LOW')
                
                if severity_order.index(current_severity) > severity_order.index(best_severity):
                    best_anomaly = anomaly
            
            unique_anomalies.append(best_anomaly)
    
    return unique_anomalies

def detect_time_based_anomalies(error_frequency: Dict[str, List[str]]) -> List[Dict[str, Any]]:
    """Legacy function for backward compatibility"""
    anomalies = []
    
    # Error spike detection
    error_counts = [len(errors) for errors in error_frequency.values()]
    if error_counts:
        mean_errors = np.mean(error_counts)
        std_errors = np.std(error_counts)
        spike_threshold = mean_errors + 2 * std_errors
        
        for timestamp, errors in error_frequency.items():
            if len(errors) > spike_threshold:
                anomalies.append({
                    'type': 'error_spike',
                    'severity': 'HIGH',
                    'confidence': 85,
                    'timestamp': timestamp,
                    'error_count': len(errors),
                    'threshold': spike_threshold,
                    'errors': errors[:3],
                    'detection_method': 'statistical_analysis'
                })
    
    return anomalies

# Backward compatibility function
def detect_anomalies(processed_data: List[Dict[str, Any]], 
                    window_size: int = 100, 
                    threshold_multiplier: float = 2.0) -> Dict[str, Any]:
    """Enhanced detect_anomalies function with advanced capabilities"""
    return detect_anomalies_advanced(
        processed_data, 
        use_ml=True, 
        use_lstm=False, 
        use_generative_ai=True
    )

# Additional helper functions for integration
def get_real_time_anomalies(buffer: RealTimeAnomalyBuffer, 
                           time_window_minutes: int = 5) -> Dict[str, Any]:
    """Get anomalies from real-time buffer"""
    recent_entries = buffer.get_recent_entries(time_window_minutes)
    return detect_anomalies_advanced(recent_entries, real_time_mode=True)

def export_anomaly_results(results: Dict[str, Any], filepath: str) -> None:
    """Export anomaly results to JSON file"""
    try:
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"Results exported to {filepath}")
    except Exception as e:
        print(f"Failed to export results: {e}")

def load_anomaly_results(filepath: str) -> Dict[str, Any]:
    """Load anomaly results from JSON file"""
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Failed to load results: {e}")
        return {}

# Performance monitoring
class PerformanceMonitor:
    """Monitor performance of anomaly detection"""
    
    def __init__(self):
        self.metrics = {
            'detection_times': [],
            'memory_usage': [],
            'accuracy_scores': [],
            'throughput': []
        }
    
    def record_detection_time(self, duration: float):
        """Record detection time"""
        self.metrics['detection_times'].append(duration)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        return {
            'avg_detection_time': np.mean(self.metrics['detection_times']) if self.metrics['detection_times'] else 0,
            'max_detection_time': max(self.metrics['detection_times']) if self.metrics['detection_times'] else 0,
            'total_detections': len(self.metrics['detection_times']),
            'throughput_avg': np.mean(self.metrics['throughput']) if self.metrics['throughput'] else 0
        }

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

# Enhanced main function with performance monitoring
def detect_anomalies_with_monitoring(processed_data: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
    """Detect anomalies with performance monitoring"""
    start_time = time.time()
    
    try:
        results = detect_anomalies_advanced(processed_data, **kwargs)
        
        # Record performance metrics
        detection_time = time.time() - start_time
        performance_monitor.record_detection_time(detection_time)
        
        # Add performance info to results
        results['performance'] = {
            'detection_time': detection_time,
            'throughput': len(processed_data) / detection_time if detection_time > 0 else 0,
            'data_points_processed': len(processed_data)
        }
        
        return results
        
    except Exception as e:
        print(f"Detection failed: {e}")
        return {
            'anomalies': [],
            'error': str(e),
            'summary': 'Detection failed due to error'
        }

# Configuration management
DEFAULT_CONFIG = {
    'contamination': 0.05,
    'use_ml': True,
    'use_lstm': False,
    'use_generative_ai': True,
    'real_time_mode': False,
    'enable_gpu': False,
    'sequence_length': 10,
    'confidence_threshold': 0.75,
    'severity_threshold': 'MEDIUM'
}

def configure_advanced_detection(config: Dict[str, Any]) -> Dict[str, Any]:
    """Configure advanced anomaly detection parameters"""
    merged_config = DEFAULT_CONFIG.copy()
    merged_config.update(config)
    return merged_config

if __name__ == "__main__":
    # Example usage and testing
    print("Advanced Anomaly Detection Module")
    print("=================================")
    
    # Test with sample data
    sample_data = [
        {
            'timestamp': '2024-01-15T10:00:00Z',
            'level': 'ERROR',
            'message': 'Connection timeout to database server',
            'service': 'web-api'
        },
        {
            'timestamp': '2024-01-15T10:01:00Z',
            'level': 'CRITICAL',
            'message': 'OutOfMemoryError in user service',
            'service': 'user-service'
        },
        {
            'timestamp': '2024-01-15T10:02:00Z',
            'level': 'ERROR',
            'message': 'Unauthorized access attempt detected',
            'service': 'auth-service'
        }
    ]
    
    print("Running test detection...")
    results = detect_anomalies_with_monitoring(sample_data)
    
    print(f"Found {results['count']} anomalies")
    print(f"Detection time: {results.get('performance', {}).get('detection_time', 0):.3f}s")
    print("Test completed successfully!")