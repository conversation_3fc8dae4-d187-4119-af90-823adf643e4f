from sklearn.metrics import confusion_matrix
import pandas as pd

class RootCauseAnalyzer:
    def __init__(self, classified_errors):
        self.classified_errors = classified_errors

    def identify_root_causes(self):
        root_causes = {}
        for error in self.classified_errors:
            cause = self._analyze_error(error)
            if cause in root_causes:
                root_causes[cause].append(error)
            else:
                root_causes[cause] = [error]
        return root_causes

    def _analyze_error(self, error):
        # Placeholder for actual analysis logic
        # This should include pattern matching or heuristic analysis
        return error.get('cause', 'Unknown')

    def evaluate_analysis(self, true_causes):
        predicted_causes = [self._analyze_error(error) for error in self.classified_errors]
        cm = confusion_matrix(true_causes, predicted_causes)
        return cm

def analyze_root_cause(classified_errors):
    """
    Analyze root causes based on classified errors with enhanced multiline support.
    
    Args:
        classified_errors: Dictionary containing classified error information
        
    Returns:
        dict: Enhanced root cause analysis results
    """
    import re
    from collections import Counter
    
    if not classified_errors or not classified_errors.get('categories'):
        return {'root_causes': {}, 'summary': 'No errors to analyze'}
    
    root_causes = {}
    detailed_analysis = {}
    
    # Enhanced root cause patterns
    root_cause_patterns = {
        'configuration_error': [
            r'config.*not found',
            r'property.*undefined',
            r'missing.*configuration',
            r'invalid.*setting'
        ],
        'resource_exhaustion': [
            r'out of memory',
            r'disk.*full',
            r'no space left',
            r'resource.*unavailable',
            r'heap.*overflow'
        ],
        'network_connectivity': [
            r'connection.*refused',
            r'timeout.*occurred',
            r'host.*unreachable',
            r'network.*error',
            r'dns.*resolution'
        ],
        'dependency_failure': [
            r'service.*unavailable',
            r'downstream.*error',
            r'external.*service',
            r'api.*timeout'
        ],
        'code_defect': [
            r'null pointer',
            r'array.*bounds',
            r'division.*zero',
            r'assertion.*failed',
            r'segmentation.*fault'
        ],
        'security_issue': [
            r'unauthorized.*access',
            r'permission.*denied',
            r'authentication.*failed',
            r'certificate.*invalid'
        ]
    }
    
    # Analyze each error category
    for category, errors in classified_errors['categories'].items():
        if not errors:
            continue
            
        category_causes = []
        cause_details = {}
        
        for error in errors:
            error_lower = error.lower()
            
            # Check for specific root cause patterns
            found_cause = False
            for cause_type, patterns in root_cause_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, error_lower):
                        category_causes.append(cause_type)
                        if cause_type not in cause_details:
                            cause_details[cause_type] = []
                        cause_details[cause_type].append(error[:100] + '...' if len(error) > 100 else error)
                        found_cause = True
                        break
                if found_cause:
                    break
            
            # Fallback to category-based analysis if no specific pattern found
            if not found_cause:
                if category == 'connection_errors':
                    category_causes.append('network_connectivity')
                elif category == 'database_errors':
                    category_causes.append('dependency_failure')
                elif category == 'authentication_errors':
                    category_causes.append('security_issue')
                elif category == 'memory_errors':
                    category_causes.append('resource_exhaustion')
                elif category == 'application_errors':
                    category_causes.append('code_defect')
                elif category == 'stack_trace_errors':
                    category_causes.append('code_defect')
                else:
                    category_causes.append('unknown_cause')
        
        root_causes[category] = category_causes
        detailed_analysis[category] = cause_details
    
    # Calculate root cause frequency
    all_causes = []
    for causes in root_causes.values():
        all_causes.extend(causes)
    
    cause_frequency = Counter(all_causes)
    
    # Generate recommendations
    recommendations = generate_recommendations(cause_frequency, classified_errors)
    
    # Calculate priority scores
    priority_scores = calculate_priority_scores(classified_errors, cause_frequency)
    
    return {
        'root_causes': root_causes,
        'detailed_analysis': detailed_analysis,
        'cause_frequency': dict(cause_frequency),
        'recommendations': recommendations,
        'priority_scores': priority_scores,
        'summary': f'Analyzed {classified_errors["total_errors"]} errors across {len(root_causes)} categories',
        'most_common_cause': cause_frequency.most_common(1)[0] if cause_frequency else None
    }

def generate_recommendations(cause_frequency, classified_errors):
    """Generate actionable recommendations based on root cause analysis"""
    recommendations = []
    
    for cause, count in cause_frequency.most_common(5):
        if cause == 'network_connectivity':
            recommendations.append({
                'cause': cause,
                'priority': 'HIGH',
                'action': 'Check network configuration, firewall rules, and DNS resolution',
                'impact': f'{count} errors detected'
            })
        elif cause == 'resource_exhaustion':
            recommendations.append({
                'cause': cause,
                'priority': 'CRITICAL',
                'action': 'Monitor resource usage, implement auto-scaling, or increase capacity',
                'impact': f'{count} errors detected'
            })
        elif cause == 'configuration_error':
            recommendations.append({
                'cause': cause,
                'priority': 'MEDIUM',
                'action': 'Review and validate configuration files',
                'impact': f'{count} errors detected'
            })
        elif cause == 'code_defect':
            recommendations.append({
                'cause': cause,
                'priority': 'HIGH',
                'action': 'Review code, add error handling, and implement unit tests',
                'impact': f'{count} errors detected'
            })
        elif cause == 'security_issue':
            recommendations.append({
                'cause': cause,
                'priority': 'CRITICAL',
                'action': 'Review security configurations, certificates, and access controls',
                'impact': f'{count} errors detected'
            })
    
    return recommendations

def calculate_priority_scores(classified_errors, cause_frequency):
    """Calculate priority scores for different root causes"""
    priority_scores = {}
    
    severity_weights = classified_errors.get('severity_scores', {})
    multiline_count = classified_errors.get('multiline_count', 0)
    
    for cause, count in cause_frequency.items():
        base_score = count * 2
        
        # Increase priority for critical causes
        if cause in ['resource_exhaustion', 'security_issue']:
            base_score *= 2
        elif cause in ['code_defect', 'network_connectivity']:
            base_score *= 1.5
        
        # Factor in multiline errors (usually more severe)
        if multiline_count > 0:
            base_score += (multiline_count * 0.5)
        
        priority_scores[cause] = min(100, base_score)
    
    return priority_scores

# Example usage:
# classified_errors = [{'error': 'Error A', 'cause': 'Cause 1'}, {'error': 'Error B', 'cause': 'Cause 2'}]
# analyzer = RootCauseAnalyzer(classified_errors)
# root_causes = analyzer.identify_root_causes()
# print(root_causes)