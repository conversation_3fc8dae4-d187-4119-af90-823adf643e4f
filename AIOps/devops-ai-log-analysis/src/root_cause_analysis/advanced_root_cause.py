import re
import numpy as np
from collections import Counter, defaultdict, deque
from datetime import datetime, timedelta
import networkx as nx
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer

class AIRootCauseAnalyzer:
    """Advanced AI-powered root cause analysis"""
    
    def __init__(self):
        self.knowledge_base = self._build_knowledge_base()
        self.correlation_graph = nx.DiGraph()
        self.pattern_vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        
    def _build_knowledge_base(self):
        """Build comprehensive knowledge base for root cause analysis"""
        return {
            'error_symptoms': {
                'connection_timeout': {
                    'possible_causes': [
                        'Network congestion or packet loss',
                        'Firewall blocking connections',
                        'Service overload or unresponsiveness',
                        'DNS resolution issues',
                        'Load balancer misconfiguration'
                    ],
                    'investigation_steps': [
                        'Check network connectivity between services',
                        'Verify firewall rules and port accessibility',
                        'Monitor service resource utilization',
                        'Test DNS resolution for target hosts',
                        'Review load balancer configuration and health checks'
                    ]
                },
                'database_error': {
                    'possible_causes': [
                        'Database server overload or resource exhaustion',
                        'Lock contention and deadlocks',
                        'Query optimization issues',
                        'Database configuration problems',
                        'Storage space exhaustion'
                    ],
                    'investigation_steps': [
                        'Check database server resource utilization',
                        'Analyze slow query logs and execution plans',
                        'Review database lock and deadlock statistics',
                        'Verify database configuration parameters',
                        'Monitor database storage space and growth'
                    ]
                },
                'memory_exhaustion': {
                    'possible_causes': [
                        'Memory leaks in application code',
                        'Insufficient memory allocation',
                        'Large dataset processing without pagination',
                        'Inefficient caching strategies',
                        'JVM heap size misconfiguration'
                    ],
                    'investigation_steps': [
                        'Analyze heap dumps and memory profiling data',
                        'Review application memory usage patterns',
                        'Check for memory leaks in long-running processes',
                        'Evaluate caching strategies and cache eviction policies',
                        'Review JVM memory configuration and GC settings'
                    ]
                },
                'authentication_failure': {
                    'possible_causes': [
                        'Expired or invalid credentials',
                        'Authentication service misconfiguration',
                        'Token validation issues',
                        'Clock synchronization problems',
                        'Certificate or key rotation issues'
                    ],
                    'investigation_steps': [
                        'Verify credential validity and expiration',
                        'Check authentication service configuration',
                        'Review token generation and validation logic',
                        'Verify system clock synchronization',
                        'Check certificate and key rotation status'
                    ]
                }
            },
            'failure_patterns': {
                'cascade_failure': {
                    'description': 'Failure in one component causing failures in dependent components',
                    'indicators': ['Multiple service failures in sequence', 'Error propagation pattern'],
                    'mitigation': ['Implement circuit breakers', 'Add bulkhead patterns', 'Improve error handling']
                },
                'resource_starvation': {
                    'description': 'System resources becoming unavailable due to high demand',
                    'indicators': ['High resource utilization', 'Queue buildup', 'Response time degradation'],
                    'mitigation': ['Implement auto-scaling', 'Add resource monitoring', 'Optimize resource usage']
                },
                'configuration_drift': {
                    'description': 'System configuration changing from expected state',
                    'indicators': ['Sudden behavior changes', 'Environment-specific issues'],
                    'mitigation': ['Implement configuration management', 'Add configuration validation', 'Use infrastructure as code']
                }
            }
        }
    
    def analyze_root_cause_advanced(self, classified_errors, historical_data=None):
        """Advanced root cause analysis with AI-powered insights"""
        if not classified_errors or not classified_errors.get('categories'):
            return {'root_causes': {}, 'summary': 'No errors to analyze'}
        
        # Build error correlation graph
        self._build_correlation_graph(classified_errors)
        
        # Perform temporal analysis
        temporal_patterns = self._analyze_temporal_patterns(classified_errors)
        
        # Identify failure cascades
        cascade_analysis = self._identify_failure_cascades(classified_errors)
        
        # Generate AI-powered hypotheses
        ai_hypotheses = self._generate_ai_hypotheses(classified_errors, temporal_patterns)
        
        # Calculate confidence scores
        confidence_scores = self._calculate_confidence_scores(classified_errors, ai_hypotheses)
        
        # Generate actionable recommendations
        recommendations = self._generate_advanced_recommendations(
            classified_errors, ai_hypotheses, confidence_scores
        )
        
        # Predict potential future issues
        risk_assessment = self._assess_future_risks(classified_errors, temporal_patterns)
        
        return {
            'root_causes': self._extract_primary_causes(ai_hypotheses),
            'ai_hypotheses': ai_hypotheses,
            'temporal_patterns': temporal_patterns,
            'cascade_analysis': cascade_analysis,
            'confidence_scores': confidence_scores,
            'recommendations': recommendations,
            'risk_assessment': risk_assessment,
            'correlation_graph': self._serialize_graph(),
            'summary': f'AI analysis of {classified_errors["total_errors"]} errors with {len(ai_hypotheses)} hypotheses generated'
        }
    
    def _build_correlation_graph(self, classified_errors):
        """Build a graph showing correlations between different error types"""
        self.correlation_graph.clear()
        
        # Add nodes for each error category
        for category, errors in classified_errors['categories'].items():
            if errors:
                self.correlation_graph.add_node(category, weight=len(errors))
        
        # Add edges based on temporal proximity and co-occurrence
        categories = list(classified_errors['categories'].keys())
        for i, cat1 in enumerate(categories):
            for cat2 in categories[i+1:]:
                correlation_strength = self._calculate_correlation(
                    classified_errors['categories'][cat1],
                    classified_errors['categories'][cat2]
                )
                if correlation_strength > 0.3:  # Threshold for significant correlation
                    self.correlation_graph.add_edge(cat1, cat2, weight=correlation_strength)
    
    def _calculate_correlation(self, errors1, errors2):
        """Calculate correlation strength between two error categories"""
        if not errors1 or not errors2:
            return 0.0
        
        # Simple correlation based on common keywords
        text1 = ' '.join(errors1).lower()
        text2 = ' '.join(errors2).lower()
        
        words1 = set(re.findall(r'\w+', text1))
        words2 = set(re.findall(r'\w+', text2))
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _analyze_temporal_patterns(self, classified_errors):
        """Analyze temporal patterns in error occurrences"""
        patterns = {
            'error_frequency_over_time': {},
            'burst_periods': [],
            'quiet_periods': [],
            'cyclical_patterns': {}
        }
        
        # Extract timestamps from errors
        timestamps = []
        for category, errors in classified_errors['categories'].items():
            for error in errors:
                # Try to extract timestamp from error message
                timestamp_match = re.search(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', error)
                if timestamp_match:
                    try:
                        dt = datetime.strptime(timestamp_match.group(), '%Y-%m-%d %H:%M:%S')
                        timestamps.append((dt, category))
                    except:
                        continue
        
        if timestamps:
            timestamps.sort()
            
            # Analyze frequency patterns
            hourly_counts = defaultdict(int)
            for dt, category in timestamps:
                hour_key = dt.strftime('%H:00')
                hourly_counts[hour_key] += 1
            
            patterns['error_frequency_over_time'] = dict(hourly_counts)
            
            # Identify burst periods (high error frequency)
            error_times = [dt for dt, _ in timestamps]
            if len(error_times) > 5:
                burst_threshold = len(error_times) * 0.1  # 10% of errors in short time
                time_windows = self._sliding_window_analysis(error_times, window_minutes=5)
                patterns['burst_periods'] = [
                    window for window, count in time_windows.items() 
                    if count > burst_threshold
                ]
        
        return patterns
    
    def _sliding_window_analysis(self, timestamps, window_minutes=5):
        """Analyze error frequency in sliding time windows"""
        if len(timestamps) < 2:
            return {}
        
        window_counts = {}
        window_size = timedelta(minutes=window_minutes)
        
        for i, start_time in enumerate(timestamps):
            end_time = start_time + window_size
            count = sum(1 for ts in timestamps[i:] if ts < end_time)
            window_key = start_time.strftime('%Y-%m-%d %H:%M:%S')
            window_counts[window_key] = count
        
        return window_counts
    
    def _identify_failure_cascades(self, classified_errors):
        """Identify potential failure cascades"""
        cascades = []
        
        # Look for patterns indicating cascade failures
        categories = classified_errors['categories']
        
        # Check for multiple error types occurring together
        non_empty_categories = [cat for cat, errors in categories.items() if errors]
        
        if len(non_empty_categories) >= 3:
            cascades.append({
                'type': 'multi_category_failure',
                'affected_categories': non_empty_categories,
                'severity': 'HIGH',
                'description': f'Multiple error categories affected: {", ".join(non_empty_categories)}'
            })
        
        # Check for high error frequency in single category (potential root cause)
        for category, errors in categories.items():
            if len(errors) > 5:  # Threshold for cascade trigger
                cascades.append({
                    'type': 'category_storm',
                    'trigger_category': category,
                    'error_count': len(errors),
                    'severity': 'MEDIUM',
                    'description': f'High error frequency in {category} may trigger cascade'
                })
        
        return cascades
    
    def _generate_ai_hypotheses(self, classified_errors, temporal_patterns):
        """Generate AI-powered root cause hypotheses"""
        hypotheses = []
        
        categories = classified_errors['categories']
        severity_scores = classified_errors.get('severity_scores', {})
        
        # Hypothesis 1: Resource exhaustion
        resource_indicators = ['memory_errors', 'timeout_errors', 'performance_errors']
        resource_score = sum(len(categories.get(cat, [])) for cat in resource_indicators)
        
        if resource_score > 3:
            hypotheses.append({
                'hypothesis': 'System resource exhaustion',
                'confidence': min(0.9, resource_score / 10),
                'evidence': [
                    f'{resource_score} resource-related errors detected',
                    'Pattern consistent with resource starvation'
                ],
                'root_cause_category': 'resource_exhaustion',
                'investigation_priority': 'HIGH'
            })
        
        # Hypothesis 2: Configuration issues
        config_indicators = ['connection_errors', 'authentication_errors', 'database_errors']
        config_score = sum(len(categories.get(cat, [])) for cat in config_indicators)
        
        if config_score > 2:
            hypotheses.append({
                'hypothesis': 'Configuration or deployment issue',
                'confidence': min(0.8, config_score / 8),
                'evidence': [
                    f'{config_score} configuration-related errors detected',
                    'Error pattern suggests misconfiguration'
                ],
                'root_cause_category': 'configuration_drift',
                'investigation_priority': 'MEDIUM'
            })
        
        # Hypothesis 3: External dependency failure
        dependency_indicators = ['network_errors', 'timeout_errors', 'connection_errors']
        dependency_score = sum(len(categories.get(cat, [])) for cat in dependency_indicators)
        
        if dependency_score > 2:
            hypotheses.append({
                'hypothesis': 'External dependency or service failure',
                'confidence': min(0.85, dependency_score / 7),
                'evidence': [
                    f'{dependency_score} dependency-related errors detected',
                    'Pattern indicates external service issues'
                ],
                'root_cause_category': 'dependency_failure',
                'investigation_priority': 'HIGH'
            })
        
        # Hypothesis 4: Security incident
        security_indicators = ['authentication_errors', 'security_errors']
        security_score = sum(len(categories.get(cat, [])) for cat in security_indicators)
        
        if security_score > 1:
            hypotheses.append({
                'hypothesis': 'Potential security incident or breach attempt',
                'confidence': min(0.95, security_score / 3),
                'evidence': [
                    f'{security_score} security-related errors detected',
                    'Pattern may indicate malicious activity'
                ],
                'root_cause_category': 'security_incident',
                'investigation_priority': 'CRITICAL'
            })
        
        # Sort hypotheses by confidence
        hypotheses.sort(key=lambda x: x['confidence'], reverse=True)
        
        return hypotheses
    
    def _calculate_confidence_scores(self, classified_errors, hypotheses):
        """Calculate confidence scores for root cause hypotheses"""
        scores = {}
        
        total_errors = classified_errors['total_errors']
        multiline_count = classified_errors.get('multiline_count', 0)
        
        for hypothesis in hypotheses:
            base_confidence = hypothesis['confidence']
            
            # Adjust confidence based on additional factors
            adjustments = 0
            
            # More multiline errors increase confidence (more detailed error info)
            if multiline_count > 0:
                adjustments += min(0.1, multiline_count / 10)
            
            # Higher total error count increases confidence
            if total_errors > 10:
                adjustments += 0.05
            
            # Priority adjustments
            priority_multipliers = {
                'CRITICAL': 1.2,
                'HIGH': 1.1,
                'MEDIUM': 1.0,
                'LOW': 0.9
            }
            
            priority = hypothesis.get('investigation_priority', 'MEDIUM')
            final_confidence = min(1.0, (base_confidence + adjustments) * priority_multipliers[priority])
            
            scores[hypothesis['hypothesis']] = {
                'confidence': final_confidence,
                'certainty_level': self._get_certainty_level(final_confidence),
                'recommendation': self._get_confidence_recommendation(final_confidence)
            }
        
        return scores
    
    def _get_certainty_level(self, confidence):
        """Convert confidence score to certainty level"""
        if confidence >= 0.9:
            return 'Very High'
        elif confidence >= 0.7:
            return 'High'
        elif confidence >= 0.5:
            return 'Medium'
        elif confidence >= 0.3:
            return 'Low'
        else:
            return 'Very Low'
    
    def _get_confidence_recommendation(self, confidence):
        """Get recommendation based on confidence level"""
        if confidence >= 0.8:
            return 'Immediate investigation recommended'
        elif confidence >= 0.6:
            return 'Further investigation warranted'
        elif confidence >= 0.4:
            return 'Consider as potential cause'
        else:
            return 'Low priority investigation'
    
    def _generate_advanced_recommendations(self, classified_errors, hypotheses, confidence_scores):
        """Generate advanced recommendations based on AI analysis"""
        recommendations = []
        
        # Sort hypotheses by confidence
        sorted_hypotheses = sorted(hypotheses, key=lambda x: x['confidence'], reverse=True)
        
        for hypothesis in sorted_hypotheses[:3]:  # Top 3 hypotheses
            root_cause_category = hypothesis['root_cause_category']
            confidence = hypothesis['confidence']
            
            if root_cause_category in self.knowledge_base['error_symptoms']:
                kb_entry = self.knowledge_base['error_symptoms'][root_cause_category]
                
                recommendation = {
                    'hypothesis': hypothesis['hypothesis'],
                    'confidence': confidence,
                    'priority': hypothesis['investigation_priority'],
                    'immediate_actions': kb_entry.get('investigation_steps', [])[:3],
                    'possible_causes': kb_entry.get('possible_causes', [])[:3],
                    'estimated_time_to_resolve': self._estimate_resolution_time(root_cause_category, confidence),
                    'required_expertise': self._get_required_expertise(root_cause_category),
                    'business_impact': self._assess_business_impact(classified_errors, root_cause_category)
                }
                
                recommendations.append(recommendation)
        
        return recommendations
    
    def _estimate_resolution_time(self, root_cause_category, confidence):
        """Estimate time to resolve based on root cause category and confidence"""
        base_times = {
            'resource_exhaustion': '2-4 hours',
            'configuration_drift': '1-2 hours',
            'dependency_failure': '3-6 hours',
            'security_incident': '4-8 hours'
        }
        
        base_time = base_times.get(root_cause_category, '2-4 hours')
        
        # Adjust based on confidence
        if confidence < 0.5:
            return f"Extended investigation needed: {base_time}"
        
        return base_time
    
    def _get_required_expertise(self, root_cause_category):
        """Get required expertise for investigating root cause"""
        expertise_map = {
            'resource_exhaustion': ['DevOps', 'System Administration', 'Performance Engineering'],
            'configuration_drift': ['DevOps', 'Configuration Management', 'System Administration'],
            'dependency_failure': ['Network Engineering', 'Service Architecture', 'DevOps'],
            'security_incident': ['Security Engineering', 'Incident Response', 'Forensics']
        }
        
        return expertise_map.get(root_cause_category, ['General IT Support'])
    
    def _assess_business_impact(self, classified_errors, root_cause_category):
        """Assess business impact of the root cause"""
        total_errors = classified_errors['total_errors']
        
        impact_levels = {
            'security_incident': 'CRITICAL - Potential data breach or security compromise',
            'resource_exhaustion': 'HIGH - Service degradation or unavailability',
            'dependency_failure': 'MEDIUM - Limited functionality impact',
            'configuration_drift': 'LOW - Isolated feature issues'
        }
        
        base_impact = impact_levels.get(root_cause_category, 'MEDIUM - Moderate service impact')
        
        # Escalate impact if many errors
        if total_errors > 20:
            if 'LOW' in base_impact:
                base_impact = base_impact.replace('LOW', 'MEDIUM')
            elif 'MEDIUM' in base_impact:
                base_impact = base_impact.replace('MEDIUM', 'HIGH')
        
        return base_impact
    
    def _assess_future_risks(self, classified_errors, temporal_patterns):
        """Assess future risks based on current error patterns"""
        risks = []
        
        total_errors = classified_errors['total_errors']
        error_categories = len([cat for cat, errors in classified_errors['categories'].items() if errors])
        
        # High error volume risk
        if total_errors > 15:
            risks.append({
                'risk_type': 'Error volume escalation',
                'probability': 'HIGH',
                'impact': 'Service degradation or outage',
                'timeframe': '1-4 hours',
                'mitigation': 'Implement immediate error monitoring and alerting'
            })
        
        # Cascade failure risk
        if error_categories >= 3:
            risks.append({
                'risk_type': 'Cascade failure propagation',
                'probability': 'MEDIUM',
                'impact': 'Multiple service components affected',
                'timeframe': '2-6 hours',
                'mitigation': 'Isolate affected components and implement circuit breakers'
            })
        
        # Performance degradation risk
        performance_errors = len(classified_errors['categories'].get('performance_errors', []))
        if performance_errors > 2:
            risks.append({
                'risk_type': 'System performance degradation',
                'probability': 'MEDIUM',
                'impact': 'User experience degradation',
                'timeframe': '1-3 hours',
                'mitigation': 'Monitor system resources and optimize performance bottlenecks'
            })
        
        return risks
    
    def _extract_primary_causes(self, hypotheses):
        """Extract primary root causes from hypotheses"""
        if not hypotheses:
            return {}
        
        primary_causes = {}
        for hypothesis in hypotheses:
            category = hypothesis['root_cause_category']
            if category not in primary_causes:
                primary_causes[category] = []
            primary_causes[category].append(hypothesis['hypothesis'])
        
        return primary_causes
    
    def _serialize_graph(self):
        """Serialize the correlation graph for output"""
        return {
            'nodes': list(self.correlation_graph.nodes(data=True)),
            'edges': list(self.correlation_graph.edges(data=True))
        }

# Updated main analysis function
def analyze_root_cause(classified_errors):
    """Enhanced root cause analysis with AI capabilities"""
    analyzer = AIRootCauseAnalyzer()
    return analyzer.analyze_root_cause_advanced(classified_errors)
