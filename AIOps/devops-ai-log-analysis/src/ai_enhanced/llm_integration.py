"""
Natural Language Query Processor for DevOps AI Log Analysis
Converts natural language queries into structured log searches.
"""

import json
import re
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NaturalLanguageQueryProcessor:
    """
    Process natural language queries and convert them to structured log queries.
    Works with or without OpenAI API - provides pattern-based fallback.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the query processor.
        
        Args:
            api_key: OpenAI API key (optional - will try environment variable)
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.use_openai = bool(self.api_key)
        
        if self.use_openai:
            try:
                import openai
                self.client = openai.OpenAI(api_key=self.api_key)
                logger.info("OpenAI integration enabled")
            except ImportError:
                logger.warning("OpenAI package not installed. Using pattern-based processing only.")
                self.use_openai = False
            except Exception as e:
                logger.warning(f"OpenAI initialization failed: {e}. Using pattern-based processing only.")
                self.use_openai = False
        else:
            logger.info("OpenAI API key not provided. Using pattern-based processing only.")
        
        # Pattern definitions for regex-based extraction
        self.query_patterns = {
            'time_range': r'(?:in the )?(?:last|past|within) (\d+) (minute|hour|day|week|month)s?',
            'service': r'(?:from|in|for) (?:service|app|application|component) (\w+)',
            'error_type': r'(error|exception|failure|crash|timeout|connection|auth|login|database|db)',
            'severity': r'(critical|error|warning|info|debug|fatal|severe)',
            'user_action': r'(login|logout|authentication|authorization|access|signup|register)',
            'count': r'(?:show|find|get) (?:me )?(?:all|top|first) (\d+)',
            'status': r'(failed|successful|success|fail|denied|blocked|allowed)',
        }
        
        # Common query templates
        self.query_templates = {
            'error_search': {
                'patterns': ['error', 'exception', 'failure', 'crash'],
                'description': 'Find error-related log entries'
            },
            'auth_search': {
                'patterns': ['auth', 'login', 'authentication', 'authorization'],
                'description': 'Find authentication-related log entries'
            },
            'service_search': {
                'patterns': ['service', 'app', 'application'],
                'description': 'Find logs from specific services'
            },
            'performance_search': {
                'patterns': ['slow', 'timeout', 'performance', 'latency'],
                'description': 'Find performance-related issues'
            }
        }
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Convert natural language query to structured search parameters.
        
        Args:
            query: Natural language query string
            
        Returns:
            Dict containing structured search parameters
        """
        logger.info(f"Processing query: {query}")
        
        # Extract basic patterns using regex
        patterns = self._extract_patterns(query)
        
        # Use LLM analysis if available
        llm_analysis = {}
        if self.use_openai:
            try:
                llm_analysis = self._analyze_with_llm(query)
            except Exception as e:
                logger.warning(f"LLM analysis failed: {e}")
        
        # Generate search filters
        search_filters = self._generate_search_filters(query, patterns, llm_analysis)
        
        # Create structured search parameters
        search_params = {
            'original_query': query,
            'time_range': patterns.get('time_range'),
            'service': patterns.get('service'),
            'error_types': patterns.get('error_types', []),
            'severity': patterns.get('severity'),
            'status': patterns.get('status'),
            'count_limit': patterns.get('count_limit', 50),
            'search_filters': search_filters,
            'keywords': self._extract_keywords(query),
            'query_type': self._determine_query_type(query),
            'confidence': self._calculate_confidence(patterns, llm_analysis),
            'llm_analysis': llm_analysis if llm_analysis else None,
            'processing_method': 'llm' if self.use_openai and llm_analysis else 'pattern'
        }
        
        logger.info(f"Generated search params with {search_params['confidence']:.2f} confidence")
        return search_params
    
    def _extract_patterns(self, query: str) -> Dict[str, Any]:
        """Extract basic patterns using regex."""
        patterns = {}
        query_lower = query.lower()
        
        # Time range extraction
        time_match = re.search(self.query_patterns['time_range'], query_lower)
        if time_match:
            amount, unit = time_match.groups()
            patterns['time_range'] = self._calculate_time_range(int(amount), unit)
        
        # Service extraction
        service_match = re.search(self.query_patterns['service'], query_lower)
        if service_match:
            patterns['service'] = service_match.group(1)
        
        # Error type extraction
        error_matches = re.findall(self.query_patterns['error_type'], query_lower)
        if error_matches:
            patterns['error_types'] = list(set(error_matches))
        
        # Severity extraction
        severity_match = re.search(self.query_patterns['severity'], query_lower)
        if severity_match:
            patterns['severity'] = severity_match.group(1).upper()
        
        # Status extraction
        status_match = re.search(self.query_patterns['status'], query_lower)
        if status_match:
            patterns['status'] = status_match.group(1)
        
        # Count limit extraction
        count_match = re.search(self.query_patterns['count'], query_lower)
        if count_match:
            patterns['count_limit'] = int(count_match.group(1))
        
        return patterns
    
    def _analyze_with_llm(self, query: str) -> Dict[str, Any]:
        """Use LLM to analyze complex queries."""
        system_prompt = """
        You are a log analysis expert. Convert natural language queries into structured search parameters for DevOps log analysis.
        
        Return a JSON object with:
        - intent: what the user is trying to find (error_analysis, performance_monitoring, security_audit, etc.)
        - keywords: array of important keywords to search for
        - filters: object with key-value pairs for filtering
        - time_sensitivity: boolean indicating if time is important
        - priority: high/medium/low based on severity indicators
        - explanation: brief explanation of what will be searched
        - suggested_actions: array of follow-up actions if issues are found
        
        Example input: "Show me all authentication failures from user-service in the last hour"
        Example output: {
            "intent": "security_audit",
            "keywords": ["authentication", "failure", "fail", "denied", "error"],
            "filters": {
                "service": "user-service",
                "event_type": "authentication",
                "status": "failure",
                "time_range": "1h"
            },
            "time_sensitivity": true,
            "priority": "high",
            "explanation": "Searching for authentication failures in user-service from the last hour - potential security issue",
            "suggested_actions": ["Check for brute force attempts", "Review user accounts", "Check IP sources"]
        }
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Query: {query}"}
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    logger.warning("Failed to parse LLM JSON response")
            
        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")
        
        return {}
    
    def _generate_search_filters(self, query: str, patterns: Dict, llm_analysis: Dict) -> Dict[str, Any]:
        """Generate search filters based on extracted patterns and LLM analysis."""
        filters = {}
        
        # Add pattern-based filters
        if patterns.get('service'):
            filters['service'] = patterns['service']
        
        if patterns.get('severity'):
            filters['severity'] = patterns['severity']
        
        if patterns.get('error_types'):
            filters['error_types'] = patterns['error_types']
        
        if patterns.get('status'):
            filters['status'] = patterns['status']
        
        # Add LLM-based filters if available
        if llm_analysis and 'filters' in llm_analysis:
            filters.update(llm_analysis['filters'])
        
        return filters
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from the query."""
        # Remove common stop words
        stop_words = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'show', 'me', 'find', 'get', 'all'}
        
        # Extract words
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _determine_query_type(self, query: str) -> str:
        """Determine the type of query based on keywords."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['error', 'exception', 'failure', 'crash']):
            return 'error_analysis'
        elif any(word in query_lower for word in ['auth', 'login', 'access', 'permission']):
            return 'security_audit'
        elif any(word in query_lower for word in ['slow', 'timeout', 'performance', 'latency']):
            return 'performance_monitoring'
        elif any(word in query_lower for word in ['service', 'app', 'component']):
            return 'service_monitoring'
        else:
            return 'general_search'
    
    def _calculate_confidence(self, patterns: Dict, llm_analysis: Dict) -> float:
        """Calculate confidence score for the query interpretation."""
        confidence = 0.0
        
        # Pattern-based confidence
        if patterns.get('time_range'):
            confidence += 0.2
        if patterns.get('service'):
            confidence += 0.2
        if patterns.get('error_types'):
            confidence += 0.2
        if patterns.get('severity'):
            confidence += 0.1
        
        # LLM-based confidence boost
        if llm_analysis:
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _calculate_time_range(self, amount: int, unit: str) -> Tuple[datetime, datetime]:
        """Calculate datetime range from amount and unit."""
        now = datetime.now()
        
        if unit.lower().startswith('minute'):
            start_time = now - timedelta(minutes=amount)
        elif unit.lower().startswith('hour'):
            start_time = now - timedelta(hours=amount)
        elif unit.lower().startswith('day'):
            start_time = now - timedelta(days=amount)
        elif unit.lower().startswith('week'):
            start_time = now - timedelta(weeks=amount)
        elif unit.lower().startswith('month'):
            start_time = now - timedelta(days=amount * 30)  # Approximate
        else:
            start_time = now - timedelta(hours=1)  # Default to 1 hour
        
        return start_time, now
    
    def get_query_suggestions(self, partial_query: str = "") -> List[str]:
        """Get query suggestions based on common patterns."""
        suggestions = [
            "Show me all errors in the last hour",
            "Find authentication failures from user-service",
            "What happened in the payment system today?",
            "Show me critical errors in the database",
            "Find all timeouts in the API gateway",
            "Show me failed login attempts in the last 30 minutes",
            "Find all exceptions in the order processing service",
            "What performance issues occurred yesterday?",
            "Show me all security alerts this week",
            "Find database connection errors in the last 24 hours"
        ]
        
        if partial_query:
            # Filter suggestions based on partial query
            partial_lower = partial_query.lower()
            filtered = [s for s in suggestions if any(word in s.lower() for word in partial_lower.split())]
            return filtered[:5]
        
        return suggestions[:5]

# Example usage and testing
if __name__ == "__main__":
    # Test the processor
    processor = NaturalLanguageQueryProcessor()
    
    test_queries = [
        "Show me all errors from the authentication service in the last hour",
        "Find critical failures in the payment system from yesterday",
        "What happened with user logins in the last 24 hours?",
        "Show me all database connection timeouts this week",
        "Find all exceptions in the API gateway",
        "What performance issues occurred in the last 30 minutes?"
    ]
    
    print("🔍 Natural Language Query Processor Test")
    print("=" * 60)
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        result = processor.process_query(query)
        print(f"Type: {result['query_type']}")
        print(f"Confidence: {result['confidence']:.2f}")
        print(f"Keywords: {result['keywords']}")
        print(f"Filters: {result['search_filters']}")
        if result['time_range']:
            start, end = result['time_range']
            print(f"Time Range: {start.strftime('%Y-%m-%d %H:%M')} to {end.strftime('%Y-%m-%d %H:%M')}")
        print("-" * 40)
    
    # Test suggestions
    print("\n🎯 Query Suggestions:")
    suggestions = processor.get_query_suggestions()
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion}")
