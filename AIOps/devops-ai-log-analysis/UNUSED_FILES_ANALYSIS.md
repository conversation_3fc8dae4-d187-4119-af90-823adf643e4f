# Unused Files Analysis

## Files Identified for Potential Deletion

### 1. Test Scripts (Standalone, not imported)
- **`basic_test.py`** - No imports found, standalone test
- **`simple_test.py`** - Self-contained test, no external imports
- **`performance_test.py`** - No imports found, standalone performance test

### 2. Old Demo/Test Files
- **`test_application.py`** - Only mentioned in setup_and_test.py as suggestion
- **`test_html_output.py`** - Standalone test for HTML output
- **`test_ui.py`** - Standalone UI test

### 3. Stack Trace Test Files (Feature-specific)
- **`test_stack_trace_detection.py`** - Standalone test for stack trace feature
- **`test_stack_trace_fix.py`** - Standalone test for stack trace fixes
- **`test_stack_trace_logs.txt`** - Test data for stack trace feature

### 4. Configuration/Setup Files
- **`create_demo_files.py`** - One-time setup script
- **`setup_and_test.py`** - One-time setup script
- **`fix_compatibility.py`** - One-time compatibility fix script

### 5. Old Demo Files
- **`demo_enhanced_pipeline.py`** - No imports found, standalone demo
- **`demo_simple_advanced.py`** - Only mentioned in documentation

### 6. Additional Files
- **`test_web_ui.py`** - Standalone web UI test
- **`advanced_anomaly_report_20250706_090915.json`** - Old report file
- **`stack_trace_demo.html`** - Static demo file

## Files to KEEP (Active/Referenced)

### Core Application Files
- **`src/main.py`** - Main application entry point
- **`src/ai_enhanced/`** - Natural language query interface
- **`ui/app.py`** - Main Flask UI application
- **`ui/chat_interface.py`** - Chat interface for NL queries
- **`demo_natural_language_queries.py`** - Active demo for NL queries
- **`run_pipeline.py`** - Pipeline execution script
- **`start_ui.py`** - UI starter script
- **`start_simple_ui.py`** - Simple UI starter script

### Data Files (Referenced in code)
- **`data/raw/logs.*`** - Referenced in multiple modules
- **`sample_queries.json`** - Used by NL query demo

### Documentation (Referenced)
- **`NATURAL_LANGUAGE_QUERY_SUCCESS.md`** - Recent implementation summary
- **`README.md`** - Main documentation
- **`requirements.txt`** - Dependencies

## Cleanup Strategy

1. **Safe to delete immediately**: Test scripts, old demos, one-time setup files
2. **Consider keeping**: Documentation files (for historical reference)
3. **Keep**: All core application files, data files, and active demos
