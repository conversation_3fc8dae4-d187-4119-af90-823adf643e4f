#!/usr/bin/env python3
"""
UI Test Script for DevOps AI Log Analysis
This script tests the web UI functionality end-to-end.
"""

import sys
import os
import time
import requests
import json
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_server():
    """Test if the UI server is running"""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✓ UI server is running and accessible")
            return True
        else:
            print(f"✗ UI server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to UI server: {e}")
        return False

def test_demo_endpoint():
    """Test the demo API endpoint"""
    try:
        response = requests.get('http://localhost:5000/api/demo', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ Demo API endpoint working correctly")
                print(f"  - Total errors: {data.get('classified_errors', {}).get('total_errors', 0)}")
                print(f"  - Categories: {len(data.get('classified_errors', {}).get('categories', {}))}")
                print(f"  - Anomalies: {data.get('anomalies', {}).get('total_anomalies', 0)}")
                print(f"  - Recommendations: {len(data.get('root_cause_analysis', {}).get('recommendations', []))}")
                return True
            else:
                print("✗ Demo API returned unsuccessful response")
                return False
        else:
            print(f"✗ Demo API returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Error testing demo API: {e}")
        return False

def test_file_upload():
    """Test file upload functionality"""
    # Create a test file
    test_file_content = """2025-01-06 10:30:15 [ERROR] Test error message
2025-01-06 10:30:16 [WARN] Test warning message
2025-01-06 10:30:17 [INFO] Test info message
2025-01-06 10:30:18 [ERROR] Database connection failed
2025-01-06 10:30:19 [ERROR] Out of memory error"""
    
    test_file_path = "test_upload.log"
    
    try:
        # Write test file
        with open(test_file_path, 'w') as f:
            f.write(test_file_content)
        
        # Upload the file
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_upload.log', f, 'text/plain')}
            response = requests.post('http://localhost:5000/upload', files=files, timeout=30)
        
        if response.status_code == 200:
            print("✓ File upload functionality working")
            return True
        else:
            print(f"✗ File upload returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Error testing file upload: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during file upload test: {e}")
        return False
    finally:
        # Clean up test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_ui_pages():
    """Test UI page accessibility"""
    pages = [
        ('/', 'Home page'),
        ('/upload', 'Upload page'),
        ('/demo', 'Demo page')
    ]
    
    all_passed = True
    for url, name in pages:
        try:
            response = requests.get(f'http://localhost:5000{url}', timeout=10)
            if response.status_code == 200:
                print(f"✓ {name} accessible")
            else:
                print(f"✗ {name} returned status code: {response.status_code}")
                all_passed = False
        except requests.exceptions.RequestException as e:
            print(f"✗ Error accessing {name}: {e}")
            all_passed = False
    
    return all_passed

def main():
    print("DevOps AI Log Analysis - UI Testing Suite")
    print("=" * 50)
    
    # Check if server is running
    if not test_ui_server():
        print("\n❌ UI server is not running!")
        print("Please start the server first:")
        print("  python start_ui.py")
        return False
    
    print("\n🧪 Running UI Tests...")
    print("-" * 30)
    
    # Test results
    tests_passed = 0
    total_tests = 4
    
    # Test 1: UI Pages
    print("\n1. Testing UI Pages...")
    if test_ui_pages():
        tests_passed += 1
    
    # Test 2: Demo API
    print("\n2. Testing Demo API...")
    if test_demo_endpoint():
        tests_passed += 1
    
    # Test 3: File Upload
    print("\n3. Testing File Upload...")
    if test_file_upload():
        tests_passed += 1
    
    # Test 4: Integration Test
    print("\n4. Running Integration Test...")
    # This would test the complete workflow
    if test_ui_server():  # Simple check for now
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! UI is working correctly.")
        print("\nYou can now:")
        print("• Open http://localhost:5000 in your browser")
        print("• Upload log files for analysis")
        print("• Try the demo mode")
        print("• Explore the interactive features")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

def check_requirements():
    """Check if all required modules are available"""
    required_modules = [
        'flask', 'werkzeug', 'requests', 'pandas', 'scikit-learn'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing required modules: {', '.join(missing_modules)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    return True

if __name__ == "__main__":
    if not check_requirements():
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)
