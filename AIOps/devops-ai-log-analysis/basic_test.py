#!/usr/bin/env python3
"""
Basic pipeline test without external dependencies that might hang.
"""

import sys
import os
import json

def create_test_data():
    """Create minimal test data in memory"""
    print("Creating test data...")
    
    # Text logs
    text_logs = [
        "2024-01-01 10:00:01 INFO Application started successfully",
        "2024-01-01 10:00:02 ERROR Database connection failed",
        "2024-01-01 10:00:03 WARN Memory usage at 85%"
    ]
    
    # JSON logs
    json_logs = [
        {
            "timestamp": "2024-01-01T10:00:01",
            "level": "INFO",
            "message": "Application started successfully",
            "service": "web-app"
        },
        {
            "timestamp": "2024-01-01T10:00:02",
            "level": "ERROR",
            "message": "Database connection failed",
            "service": "database"
        }
    ]
    
    return text_logs, json_logs

def test_basic_functions():
    """Test basic functionality without file I/O"""
    print("Testing basic functions...")
    
    # Add src to path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    try:
        # Test text preprocessing
        from preprocessing.preprocess_text import preprocess_text
        text_logs, json_logs = create_test_data()
        
        processed_text = preprocess_text(text_logs)
        print(f"✓ Text preprocessing: {len(processed_text)} entries")
        
        # Test JSON preprocessing
        from preprocessing.preprocess_json import preprocess_json
        processed_json = preprocess_json(json_logs)
        print(f"✓ JSON preprocessing: {len(processed_json)} entries")
        
        # Test simple error classification
        from error_classification.classify_errors import classify_errors
        classification_result = classify_errors(processed_text)
        print(f"✓ Error classification: {classification_result}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("DevOps AI Log Analysis - Basic Test")
    print("=" * 40)
    
    success = test_basic_functions()
    
    if success:
        print("\n✅ Basic tests completed successfully!")
        print("\nThe application core functions are working.")
        print("\nTo run with real data files:")
        print("1. Use VS Code's Python extension")
        print("2. Right-click on a .py file and select 'Run Python File in Terminal'")
        print("3. Or open a new terminal and try again")
    else:
        print("\n❌ Tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
