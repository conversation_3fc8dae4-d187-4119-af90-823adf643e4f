{"error_analysis": ["Show me all errors in the last hour", "Find critical failures in the payment system", "What exceptions occurred in the API gateway?", "Show me database connection errors from yesterday"], "security_audit": ["Find authentication failures from user-service", "Show me failed login attempts in the last 30 minutes", "What security alerts happened this week?", "Find all unauthorized access attempts"], "performance_monitoring": ["Show me all timeouts in the last 24 hours", "Find slow database queries from today", "What performance issues occurred yesterday?", "Show me high latency requests"], "service_monitoring": ["What happened in the order processing service?", "Show me all logs from the payment gateway", "Find issues in the notification service", "What's happening with the user management system?"], "general_search": ["Show me what happened in the last hour", "Find all important events from today", "What's been happening with the system?", "Show me recent activity"]}