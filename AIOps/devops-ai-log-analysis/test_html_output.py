#!/usr/bin/env python3
"""
Test script to verify the HTML output contains the full stack traces
"""

import requests
import re
from bs4 import BeautifulSoup

def test_html_output():
    """Test that the HTML output contains full stack traces"""
    
    # Upload the test file
    print("Uploading test file...")
    session = requests.Session()
    
    with open('test_stack_trace_logs.txt', 'rb') as f:
        files = {'file': ('test_stack_trace_logs.txt', f, 'text/plain')}
        response = session.post('http://localhost:5001/upload', files=files)
    
    if response.status_code != 200:
        print(f"❌ Upload failed with status {response.status_code}")
        return False
    
    # Get the analysis page
    print("Fetching analysis page...")
    analysis_response = session.get('http://localhost:5001/analysis')
    
    if analysis_response.status_code != 200:
        print(f"❌ Analysis page failed with status {analysis_response.status_code}")
        return False
    
    html_content = analysis_response.text
    
    # Parse the HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all anomaly items
    anomaly_items = soup.find_all('div', class_='anomaly-item')
    print(f"Found {len(anomaly_items)} anomaly items")
    
    stack_trace_found = False
    full_trace_found = False
    
    for i, item in enumerate(anomaly_items):
        print(f"\nAnomalies {i+1}:")
        
        # Check message
        message = item.find('p')
        if message:
            print(f"  Message: {message.text.strip()}")
            
            # Check if it's a stack trace message
            if 'stack trace' in message.text.lower():
                stack_trace_found = True
                print("  ✅ Found stack trace anomaly")
                
                # Look for the full trace code block
                code_block = item.find('pre', class_='stack-trace-block')
                if code_block:
                    trace_content = code_block.get_text()
                    lines = trace_content.split('\n')
                    print(f"  ✅ Found full trace with {len(lines)} lines")
                    print(f"  Preview: {trace_content[:100]}...")
                    
                    # Check if it contains multiple lines (real stack trace)
                    if len(lines) > 2:
                        full_trace_found = True
                        print("  ✅ Full multiline stack trace detected!")
                    else:
                        print("  ❌ Only single line found")
                else:
                    print("  ❌ No code block found")
    
    print(f"\nResults:")
    print(f"  Stack trace anomaly found: {stack_trace_found}")
    print(f"  Full multiline trace found: {full_trace_found}")
    
    return stack_trace_found and full_trace_found

if __name__ == "__main__":
    try:
        success = test_html_output()
        if success:
            print("\n🎉 HTML output test PASSED! Full stack traces are being displayed.")
        else:
            print("\n❌ HTML output test FAILED! Stack traces are not being displayed properly.")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
