#!/usr/bin/env python3
"""
Demo setup script for the DevOps AI Log Analysis Web UI
This script creates sample log files for testing the UI functionality.
"""

import os
import json
import time
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_sample_log_files():
    """Create sample log files for testing"""
    
    # Ensure data/raw directory exists
    os.makedirs("data/raw", exist_ok=True)
    
    # Sample text log file
    sample_text_log = """2025-01-06 10:30:15 [ERROR] Database connection failed: Connection refused to mysql://localhost:3306/app_db
2025-01-06 10:30:16 [WARN] Retrying database connection in 5 seconds...
2025-01-06 10:30:21 [ERROR] Database connection failed: Connection refused to mysql://localhost:3306/app_db
2025-01-06 10:30:22 [CRITICAL] Out of memory error in application heap
2025-01-06 10:30:23 [ERROR] Authentication failed for user 'admin': Invalid credentials
2025-01-06 10:30:24 [ERROR] Permission denied accessing /var/log/application.log
2025-01-06 10:30:25 [ERROR] Network timeout after 30 seconds
2025-01-06 10:30:26 [ERROR] Service 'user-service' is unavailable: 503 Service Temporarily Unavailable
2025-01-06 10:30:27 [ERROR] API timeout occurred while calling external service
2025-01-06 10:30:28 [ERROR] Configuration file config.properties not found
2025-01-06 10:30:29 [ERROR] Null pointer exception in UserController.java:45
2025-01-06 10:30:30 [ERROR] Array index out of bounds in DataProcessor.java:123
2025-01-06 10:30:31 [ERROR] Division by zero in Calculator.java:67
2025-01-06 10:30:32 [ERROR] Heap space exhausted: OutOfMemoryError
2025-01-06 10:30:33 [ERROR] DNS resolution failed for api.example.com
2025-01-06 10:30:34 [ERROR] Certificate validation failed: SSL handshake error
2025-01-06 10:30:35 [ERROR] Disk space full: No space left on device
2025-01-06 10:30:36 [ERROR] Resource pool exhausted: Connection pool is full
2025-01-06 10:30:37 [ERROR] Invalid configuration property: database.url is undefined
2025-01-06 10:30:38 [ERROR] Security violation: Unauthorized access attempt from IP *************
2025-01-06 10:30:39 [ERROR] Stack trace detected:
java.lang.NullPointerException: Cannot invoke method on null object
    at com.example.UserService.processUser(UserService.java:45)
    at com.example.UserController.handleRequest(UserController.java:123)
    at com.example.WebServer.processRequest(WebServer.java:89)
    at java.base/java.lang.Thread.run(Thread.java:834)
2025-01-06 10:30:40 [ERROR] Stack overflow error in recursive function
2025-01-06 10:30:41 [INFO] Application started successfully
2025-01-06 10:30:42 [DEBUG] Processing user request for user ID: 12345
2025-01-06 10:30:43 [INFO] User authentication successful for user 'john.doe'
2025-01-06 10:30:44 [DEBUG] Database query executed successfully
2025-01-06 10:30:45 [INFO] Response sent to client with status 200
2025-01-06 10:30:46 [ERROR] Python traceback detected:
Traceback (most recent call last):
  File "/app/main.py", line 123, in process_data
    result = calculate_metrics(data)
  File "/app/utils.py", line 67, in calculate_metrics
    return data['value'] / data['count']
ZeroDivisionError: division by zero
2025-01-06 10:30:47 [ERROR] Assertion failed: Expected value != null but was null
2025-01-06 10:30:48 [ERROR] Segmentation fault in native library
2025-01-06 10:30:49 [ERROR] External API returned error 500: Internal Server Error
2025-01-06 10:30:50 [ERROR] Missing required configuration setting: app.secret.key
2025-01-06 10:30:51 [ERROR] Host unreachable: No route to host ********
2025-01-06 10:30:52 [ERROR] File not found: /etc/app/config.xml"""
    
    # Sample JSON log file
    sample_json_log = [
        {
            "timestamp": "2025-01-06T10:30:15Z",
            "level": "ERROR",
            "message": "Database connection failed",
            "details": {
                "error": "Connection refused",
                "host": "localhost",
                "port": 3306,
                "database": "app_db"
            },
            "source": "DatabaseManager"
        },
        {
            "timestamp": "2025-01-06T10:30:22Z",
            "level": "CRITICAL",
            "message": "Out of memory error",
            "details": {
                "error": "OutOfMemoryError",
                "heap_size": "512MB",
                "used_memory": "510MB"
            },
            "source": "ApplicationHeap"
        },
        {
            "timestamp": "2025-01-06T10:30:23Z",
            "level": "ERROR",
            "message": "Authentication failed",
            "details": {
                "user": "admin",
                "reason": "Invalid credentials",
                "attempts": 3
            },
            "source": "AuthenticationService"
        },
        {
            "timestamp": "2025-01-06T10:30:25Z",
            "level": "ERROR",
            "message": "Network timeout",
            "details": {
                "timeout": "30 seconds",
                "endpoint": "api.example.com",
                "operation": "POST /users"
            },
            "source": "NetworkClient"
        },
        {
            "timestamp": "2025-01-06T10:30:29Z",
            "level": "ERROR",
            "message": "Null pointer exception",
            "details": {
                "class": "UserController",
                "method": "processUser",
                "line": 45,
                "stack_trace": "java.lang.NullPointerException at UserController.java:45"
            },
            "source": "ApplicationError"
        }
    ]
    
    # Sample XML log file
    sample_xml_log = """<?xml version="1.0" encoding="UTF-8"?>
<logs>
    <log>
        <timestamp>2025-01-06T10:30:15Z</timestamp>
        <level>ERROR</level>
        <message>Database connection failed</message>
        <source>DatabaseManager</source>
        <details>
            <error>Connection refused</error>
            <host>localhost</host>
            <port>3306</port>
        </details>
    </log>
    <log>
        <timestamp>2025-01-06T10:30:22Z</timestamp>
        <level>CRITICAL</level>
        <message>Out of memory error</message>
        <source>ApplicationHeap</source>
        <details>
            <error>OutOfMemoryError</error>
            <heap_size>512MB</heap_size>
            <used_memory>510MB</used_memory>
        </details>
    </log>
    <log>
        <timestamp>2025-01-06T10:30:23Z</timestamp>
        <level>ERROR</level>
        <message>Authentication failed</message>
        <source>AuthenticationService</source>
        <details>
            <user>admin</user>
            <reason>Invalid credentials</reason>
            <attempts>3</attempts>
        </details>
    </log>
    <log>
        <timestamp>2025-01-06T10:30:25Z</timestamp>
        <level>ERROR</level>
        <message>Network timeout</message>
        <source>NetworkClient</source>
        <details>
            <timeout>30 seconds</timeout>
            <endpoint>api.example.com</endpoint>
            <operation>POST /users</operation>
        </details>
    </log>
    <log>
        <timestamp>2025-01-06T10:30:29Z</timestamp>
        <level>ERROR</level>
        <message>Null pointer exception</message>
        <source>ApplicationError</source>
        <details>
            <class>UserController</class>
            <method>processUser</method>
            <line>45</line>
            <stack_trace>java.lang.NullPointerException at UserController.java:45</stack_trace>
        </details>
    </log>
</logs>"""
    
    # Write sample files
    with open("data/raw/sample_text_logs.txt", "w") as f:
        f.write(sample_text_log)
    
    with open("data/raw/sample_json_logs.json", "w") as f:
        json.dump(sample_json_log, f, indent=2)
    
    with open("data/raw/sample_xml_logs.xml", "w") as f:
        f.write(sample_xml_log)
    
    print("✓ Created sample log files:")
    print("  - data/raw/sample_text_logs.txt")
    print("  - data/raw/sample_json_logs.json")
    print("  - data/raw/sample_xml_logs.xml")

def create_multiline_log():
    """Create a multiline log file with stack traces"""
    multiline_log = """2025-01-06 10:30:15 [INFO] Application starting up...
2025-01-06 10:30:16 [INFO] Loading configuration from config.properties
2025-01-06 10:30:17 [ERROR] Failed to connect to database
java.sql.SQLException: Connection refused
    at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
    at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
    at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
    at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
    at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
    at java.sql.DriverManager.getConnection(DriverManager.java:664)
    at com.example.DatabaseManager.connect(DatabaseManager.java:45)
    at com.example.App.main(App.java:23)
2025-01-06 10:30:18 [WARN] Retrying database connection...
2025-01-06 10:30:19 [ERROR] Authentication service unavailable
com.example.AuthenticationException: Service temporarily unavailable
    at com.example.AuthService.authenticate(AuthService.java:67)
    at com.example.UserController.login(UserController.java:89)
    at com.example.WebServer.handleRequest(WebServer.java:123)
    at java.base/java.lang.Thread.run(Thread.java:834)
Caused by: java.net.ConnectException: Connection refused
    at java.base/java.net.PlainSocketImpl.socketConnect(Native Method)
    at java.base/java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:412)
    at java.base/java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:255)
    at java.base/java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:237)
    at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
    at java.base/java.net.Socket.connect(Socket.java:609)
    at com.example.AuthService.authenticate(AuthService.java:65)
    ... 3 more
2025-01-06 10:30:20 [ERROR] Memory allocation failed
java.lang.OutOfMemoryError: Java heap space
    at java.base/java.util.Arrays.copyOf(Arrays.java:3512)
    at java.base/java.util.Arrays.copyOf(Arrays.java:3481)
    at java.base/java.util.ArrayList.grow(ArrayList.java:237)
    at java.base/java.util.ArrayList.ensureCapacityInternal(ArrayList.java:229)
    at java.base/java.util.ArrayList.add(ArrayList.java:440)
    at com.example.DataProcessor.processLargeDataset(DataProcessor.java:156)
    at com.example.BatchJob.run(BatchJob.java:78)
    at java.base/java.lang.Thread.run(Thread.java:834)
2025-01-06 10:30:21 [INFO] Attempting graceful shutdown...
2025-01-06 10:30:22 [ERROR] Shutdown failed - forced termination
2025-01-06 10:30:23 [INFO] Application terminated"""
    
    with open("data/raw/multiline_logs.txt", "w") as f:
        f.write(multiline_log)
    
    print("✓ Created multiline log file:")
    print("  - data/raw/multiline_logs.txt")

def main():
    print("Creating sample log files for UI testing...")
    print("=" * 50)
    
    create_sample_log_files()
    create_multiline_log()
    
    print("\n✓ All sample files created successfully!")
    print("\nYou can now:")
    print("1. Start the UI server: python start_ui.py")
    print("2. Upload these sample files to test the interface")
    print("3. Use the demo mode to see pre-loaded analysis results")

if __name__ == "__main__":
    main()
