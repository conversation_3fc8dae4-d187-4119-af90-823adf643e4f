#!/usr/bin/env python3
"""
Setup and test script for DevOps AI Log Analysis.
This script sets up the environment and runs tests without requiring terminal interaction.
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("⚠ Warning: Python 3.7+ recommended")
    else:
        print("✓ Python version is compatible")

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'numpy', 'pandas', 'scikit-learn', 'matplotlib', 
        'seaborn', 'nltk', 'xmltodict', 'jsonschema'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is missing")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install " + " ".join(missing_packages))
        return False
    return True

def create_sample_data():
    """Create sample data files if they don't exist"""
    print("\nCreating sample data files...")
    
    # Create directories
    os.makedirs('data/raw', exist_ok=True)
    os.makedirs('data/processed', exist_ok=True)
    
    # Create text logs
    if not os.path.exists('data/raw/text_logs.txt'):
        with open('data/raw/text_logs.txt', 'w') as f:
            f.write('''2024-01-01 10:00:01 INFO Application started successfully
2024-01-01 10:00:02 INFO Database connection established
2024-01-01 10:00:03 ERROR Failed to connect to Redis server: Connection timeout
2024-01-01 10:00:04 WARN Memory usage is at 85%
2024-01-01 10:00:05 INFO Processing user request for user_id=12345
2024-01-01 10:00:06 ERROR SQL query failed: Table 'users' doesn't exist
2024-01-01 10:00:07 INFO Request processed successfully
2024-01-01 10:00:08 ERROR HTTP 500 Internal Server Error
2024-01-01 10:00:09 INFO Scheduled backup started
2024-01-01 10:00:10 ERROR Backup failed: Insufficient disk space
2024-01-01 10:00:11 WARN CPU usage spike detected: 95%
2024-01-01 10:00:12 INFO User authentication successful
2024-01-01 10:00:13 ERROR Authentication failed: Invalid credentials
2024-01-01 10:00:14 INFO API endpoint /health responded with 200
2024-01-01 10:00:15 ERROR Network timeout occurred''')
        print("✓ Created text_logs.txt")
    
    # Create JSON logs
    if not os.path.exists('data/raw/logs.json'):
        with open('data/raw/logs.json', 'w') as f:
            f.write('''[
  {
    "timestamp": "2024-01-01T10:00:01",
    "level": "INFO",
    "message": "Application started successfully",
    "service": "web-app"
  },
  {
    "timestamp": "2024-01-01T10:00:03",
    "level": "ERROR",
    "message": "Failed to connect to Redis server: Connection timeout",
    "service": "cache"
  },
  {
    "timestamp": "2024-01-01T10:00:06",
    "level": "ERROR",
    "message": "SQL query failed: Table 'users' doesn't exist",
    "service": "database"
  },
  {
    "timestamp": "2024-01-01T10:00:08",
    "level": "ERROR",
    "message": "HTTP 500 Internal Server Error",
    "service": "web-app"
  },
  {
    "timestamp": "2024-01-01T10:00:13",
    "level": "ERROR",
    "message": "Authentication failed: Invalid credentials",
    "service": "auth"
  }
]''')
        print("✓ Created logs.json")
    
    # Create XML logs
    if not os.path.exists('data/raw/logs.xml'):
        with open('data/raw/logs.xml', 'w') as f:
            f.write('''<?xml version="1.0" encoding="UTF-8"?>
<logs>
  <log>
    <timestamp>2024-01-01T10:00:01</timestamp>
    <level>INFO</level>
    <message>Application started successfully</message>
    <service>web-app</service>
  </log>
  <log>
    <timestamp>2024-01-01T10:00:03</timestamp>
    <level>ERROR</level>
    <message>Failed to connect to Redis server: Connection timeout</message>
    <service>cache</service>
  </log>
  <log>
    <timestamp>2024-01-01T10:00:06</timestamp>
    <level>ERROR</level>
    <message>SQL query failed: Table 'users' doesn't exist</message>
    <service>database</service>
  </log>
</logs>''')
        print("✓ Created logs.xml")
    
    # Create CSV logs
    if not os.path.exists('data/raw/timeseries_logs.csv'):
        with open('data/raw/timeseries_logs.csv', 'w') as f:
            f.write('''timestamp,cpu_usage,memory_usage,response_time,error_count
2024-01-01 10:00:01,45.2,72.3,150,0
2024-01-01 10:00:02,48.1,73.1,145,0
2024-01-01 10:00:03,52.3,74.8,160,1
2024-01-01 10:00:04,56.7,76.2,155,0
2024-01-01 10:00:05,61.2,78.5,175,0
2024-01-01 10:00:06,65.8,80.1,180,1
2024-01-01 10:00:07,69.4,81.7,165,0
2024-01-01 10:00:08,73.1,83.2,200,1
2024-01-01 10:00:09,77.8,84.9,190,0
2024-01-01 10:00:10,82.5,86.3,210,1
2024-01-01 10:00:11,95.2,88.1,250,0
2024-01-01 10:00:12,58.3,75.4,140,0
2024-01-01 10:00:13,62.1,77.2,158,1
2024-01-01 10:00:14,55.7,74.8,135,0
2024-01-01 10:00:15,59.4,76.1,145,1''')
        print("✓ Created timeseries_logs.csv")

def run_tests():
    """Run the application tests"""
    print("\n" + "=" * 50)
    print("RUNNING APPLICATION TESTS")
    print("=" * 50)
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Import and run the pipeline
        exec(open('run_pipeline.py').read())
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main setup and test function"""
    print("DevOps AI Log Analysis - Setup and Test")
    print("=" * 50)
    
    # Check Python version
    check_python_version()
    
    # Check dependencies
    if not check_dependencies():
        print("\n⚠ Please install missing dependencies before proceeding")
        return
    
    # Create sample data
    create_sample_data()
    
    # Run tests
    run_tests()
    
    print("\n" + "=" * 50)
    print("✓ Setup and testing completed!")
    print("\nNext steps:")
    print("1. Run: python run_pipeline.py")
    print("2. Or run: python test_application.py")
    print("3. Modify the code in src/ directory as needed")
    print("4. Add your own log files to data/raw/")

if __name__ == "__main__":
    main()
