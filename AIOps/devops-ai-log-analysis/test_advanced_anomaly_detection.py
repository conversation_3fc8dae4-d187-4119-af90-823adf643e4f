#!/usr/bin/env python3
"""
Advanced Anomaly Detection Testing Script
=========================================

This script thoroughly tests the enhanced anomaly detection system with:
- Multiple detection algorithms (ML, LSTM, Pattern-based)
- Real-time processing capabilities
- AI-powered insights and recommendations
- Performance monitoring
- Large-scale data handling
"""

import sys
import os
import time
import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from anomaly_detection.advanced_anomaly_detection import (
        detect_anomalies_advanced,
        detect_anomalies_with_monitoring,
        AdvancedAnomalyDetector,
        GenerativeAIAnalyzer,
        RealTimeAnomalyBuffer,
        performance_monitor,
        configure_advanced_detection
    )
    print("✓ Successfully imported advanced anomaly detection modules")
except ImportError as e:
    print(f"✗ Failed to import advanced modules: {e}")
    sys.exit(1)

def generate_test_data(num_entries: int = 1000) -> List[Dict[str, Any]]:
    """Generate comprehensive test data with various anomaly patterns"""
    
    services = ['web-api', 'user-service', 'auth-service', 'database', 'cache', 'notification']
    levels = ['INFO', 'WARN', 'ERROR', 'CRITICAL', 'DEBUG']
    
    # Normal message patterns
    normal_patterns = [
        "User logged in successfully",
        "API request processed",
        "Cache hit for key {key}",
        "Database query executed",
        "Email sent to user",
        "File uploaded successfully",
        "Configuration updated",
        "Health check passed",
        "Request completed in {time}ms",
        "Session created for user {user}"
    ]
    
    # Anomaly patterns
    anomaly_patterns = [
        "OutOfMemoryError: heap space exhausted",
        "Connection timeout to external service",
        "Unauthorized access attempt from IP {ip}",
        "Database connection pool exhausted",
        "Critical system failure detected",
        "Malicious payload detected in request",
        "Security breach: multiple failed login attempts",
        "Performance degradation: response time {time}ms",
        "Data corruption detected in file {file}",
        "Cascade failure: multiple services down",
        "Memory leak detected in {service}",
        "Disk space critical: {percentage}% full",
        "Network unreachable: {host}",
        "SQL injection attempt blocked",
        "Certificate expired for {domain}",
        "Rate limit exceeded for {endpoint}"
    ]
    
    data = []
    base_time = datetime.now()
    
    for i in range(num_entries):
        # Generate timestamp
        timestamp = base_time + timedelta(minutes=i // 10)
        
        # Determine if this should be an anomaly (10% chance)
        is_anomaly = random.random() < 0.1
        
        if is_anomaly:
            level = random.choice(['ERROR', 'CRITICAL'])
            message = random.choice(anomaly_patterns)
            
            # Fill in placeholders
            message = message.replace('{key}', f"user_{random.randint(1000, 9999)}")
            message = message.replace('{time}', str(random.randint(5000, 15000)))
            message = message.replace('{user}', f"user_{random.randint(100, 999)}")
            message = message.replace('{ip}', f"192.168.1.{random.randint(1, 255)}")
            message = message.replace('{service}', random.choice(services))
            message = message.replace('{file}', f"data_{random.randint(1, 100)}.txt")
            message = message.replace('{percentage}', str(random.randint(85, 98)))
            message = message.replace('{host}', f"server{random.randint(1, 10)}.example.com")
            message = message.replace('{endpoint}', f"/api/v1/{random.choice(['users', 'orders', 'products'])}")
            message = message.replace('{domain}', f"{random.choice(['api', 'web', 'cdn'])}.example.com")
        else:
            level = random.choice(['INFO', 'WARN', 'DEBUG'])
            message = random.choice(normal_patterns)
            
            # Fill in placeholders
            message = message.replace('{key}', f"user_{random.randint(1000, 9999)}")
            message = message.replace('{time}', str(random.randint(50, 500)))
            message = message.replace('{user}', f"user_{random.randint(100, 999)}")
        
        entry = {
            'timestamp': timestamp.isoformat() + 'Z',
            'level': level,
            'message': message,
            'service': random.choice(services),
            'host': f"server{random.randint(1, 5)}.example.com",
            'thread': f"thread-{random.randint(1, 20)}"
        }
        
        data.append(entry)
    
    return data

def test_basic_detection():
    """Test basic anomaly detection functionality"""
    print("\n🔍 Testing Basic Anomaly Detection")
    print("=" * 50)
    
    # Generate test data
    test_data = generate_test_data(100)
    print(f"Generated {len(test_data)} test entries")
    
    # Run detection
    results = detect_anomalies_advanced(test_data)
    
    # Verify results
    print(f"✓ Detected {results['count']} anomalies")
    print(f"✓ Severity distribution: {results['severity_distribution']}")
    print(f"✓ Detection methods: {results['detection_methods']}")
    print(f"✓ Average confidence: {results['statistics']['avg_confidence']:.2f}")
    
    # Check that we have some anomalies
    if results['count'] > 0:
        print("✓ Basic detection test passed")
        return True
    else:
        print("✗ Basic detection test failed - no anomalies detected")
        return False

def test_ml_detection():
    """Test machine learning based detection"""
    print("\n🤖 Testing ML-Based Anomaly Detection")
    print("=" * 50)
    
    # Generate larger dataset for ML
    test_data = generate_test_data(500)
    print(f"Generated {len(test_data)} test entries for ML training")
    
    # Test with ML enabled
    results = detect_anomalies_advanced(
        test_data, 
        use_ml=True, 
        use_lstm=False, 
        use_generative_ai=True
    )
    
    print(f"✓ ML detection found {results['count']} anomalies")
    print(f"✓ Detection methods used: {list(results['detection_methods'].keys())}")
    
    # Check for ML-specific detection methods
    ml_methods = [method for method in results['detection_methods'].keys() 
                  if 'ml' in method.lower() or 'ensemble' in method.lower()]
    
    if ml_methods:
        print(f"✓ ML methods successfully used: {ml_methods}")
        return True
    else:
        print("✗ ML detection test failed - no ML methods detected")
        return False

def test_real_time_detection():
    """Test real-time anomaly detection capabilities"""
    print("\n⚡ Testing Real-Time Anomaly Detection")
    print("=" * 50)
    
    # Create real-time buffer
    buffer = RealTimeAnomalyBuffer(max_size=1000)
    
    # Simulate real-time log streaming
    print("Simulating real-time log streaming...")
    
    for i in range(50):
        # Generate a log entry
        entry = {
            'timestamp': datetime.now().isoformat() + 'Z',
            'level': random.choice(['INFO', 'WARN', 'ERROR']),
            'message': f"Real-time entry {i}: " + random.choice([
                "Normal operation",
                "WARNING: High memory usage",
                "ERROR: Connection failed",
                "Critical system alert"
            ]),
            'service': random.choice(['web-api', 'database', 'cache'])
        }
        
        buffer.add_entry(entry)
        
        # Small delay to simulate real-time
        time.sleep(0.01)
    
    # Get recent entries and analyze
    recent_entries = buffer.get_recent_entries(minutes=5)
    print(f"✓ Buffer contains {len(recent_entries)} recent entries")
    
    # Run real-time detection
    results = detect_anomalies_advanced(recent_entries, real_time_mode=True)
    
    print(f"✓ Real-time detection found {results['count']} anomalies")
    print(f"✓ Performance: {results.get('performance_metrics', {})}")
    
    return True

def test_ai_insights():
    """Test AI-powered insights and recommendations"""
    print("\n🧠 Testing AI-Powered Insights")
    print("=" * 50)
    
    # Generate data with specific patterns
    test_data = []
    
    # Add security-related anomalies
    for i in range(5):
        test_data.append({
            'timestamp': datetime.now().isoformat() + 'Z',
            'level': 'ERROR',
            'message': f'Unauthorized access attempt from IP 192.168.1.{100 + i}',
            'service': 'auth-service'
        })
    
    # Add performance issues
    for i in range(3):
        test_data.append({
            'timestamp': datetime.now().isoformat() + 'Z',
            'level': 'WARN',
            'message': f'Response time {5000 + i * 1000}ms exceeds threshold',
            'service': 'web-api'
        })
    
    # Add memory issues
    for i in range(2):
        test_data.append({
            'timestamp': datetime.now().isoformat() + 'Z',
            'level': 'CRITICAL',
            'message': f'OutOfMemoryError in service {i}',
            'service': 'user-service'
        })
    
    # Run detection with AI insights
    results = detect_anomalies_advanced(test_data, use_generative_ai=True)
    
    print(f"✓ AI analysis processed {len(test_data)} entries")
    print(f"✓ Found {results['count']} anomalies")
    
    # Check AI insights
    ai_insights = results.get('ai_insights', {})
    if ai_insights:
        print("✓ AI insights generated:")
        for key, value in ai_insights.items():
            if isinstance(value, dict) and value:
                print(f"  - {key}: {len(value)} items")
            elif isinstance(value, list) and value:
                print(f"  - {key}: {len(value)} items")
    
    # Check recommendations
    recommendations = results.get('ai_recommendations', [])
    if recommendations:
        print(f"✓ Generated {len(recommendations)} AI recommendations")
        for rec in recommendations[:3]:  # Show first 3
            print(f"  - {rec.get('category', 'Unknown')}: {rec.get('priority', 'Unknown')} priority")
    
    return len(recommendations) > 0

def test_performance_monitoring():
    """Test performance monitoring capabilities"""
    print("\n📊 Testing Performance Monitoring")
    print("=" * 50)
    
    # Test with different data sizes
    data_sizes = [100, 500, 1000]
    
    for size in data_sizes:
        print(f"Testing with {size} entries...")
        
        test_data = generate_test_data(size)
        start_time = time.time()
        
        results = detect_anomalies_with_monitoring(test_data)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"  ✓ Processed {size} entries in {duration:.3f}s")
        print(f"  ✓ Throughput: {size/duration:.1f} entries/sec")
        print(f"  ✓ Found {results['count']} anomalies")
        
        # Check performance metrics
        perf_metrics = results.get('performance', {})
        if perf_metrics:
            print(f"  ✓ Detection time: {perf_metrics.get('detection_time', 0):.3f}s")
            print(f"  ✓ Throughput: {perf_metrics.get('throughput', 0):.1f} entries/sec")
    
    # Get overall performance summary
    summary = performance_monitor.get_performance_summary()
    print(f"\n✓ Performance Summary:")
    print(f"  - Total detections: {summary['total_detections']}")
    print(f"  - Average detection time: {summary['avg_detection_time']:.3f}s")
    print(f"  - Max detection time: {summary['max_detection_time']:.3f}s")
    
    return True

def test_configuration():
    """Test configuration management"""
    print("\n⚙️ Testing Configuration Management")
    print("=" * 50)
    
    # Test custom configuration
    custom_config = {
        'use_ml': True,
        'use_lstm': False,
        'use_generative_ai': True,
        'real_time_mode': False
    }
    
    config = configure_advanced_detection(custom_config)
    print(f"✓ Configuration applied: {config}")
    
    # Test with configuration (only pass supported parameters)
    test_data = generate_test_data(100)
    supported_params = {
        'use_ml': config['use_ml'],
        'use_lstm': config['use_lstm'],
        'use_generative_ai': config['use_generative_ai'],
        'real_time_mode': config['real_time_mode']
    }
    
    results = detect_anomalies_advanced(test_data, **supported_params)
    
    print(f"✓ Detection with custom config found {results['count']} anomalies")
    
    return True

def test_export_import():
    """Test result export and import functionality"""
    print("\n💾 Testing Export/Import Functionality")
    print("=" * 50)
    
    # Generate test data and run detection
    test_data = generate_test_data(100)
    results = detect_anomalies_advanced(test_data)
    
    # Export results
    export_path = "/tmp/test_anomaly_results.json"
    
    try:
        from anomaly_detection.advanced_anomaly_detection import export_anomaly_results, load_anomaly_results
        
        export_anomaly_results(results, export_path)
        print(f"✓ Results exported to {export_path}")
        
        # Import results
        loaded_results = load_anomaly_results(export_path)
        print(f"✓ Results loaded from {export_path}")
        
        # Verify integrity
        if loaded_results.get('count') == results.get('count'):
            print("✓ Export/import integrity verified")
            
            # Cleanup
            if os.path.exists(export_path):
                os.remove(export_path)
            
            return True
        else:
            print("✗ Export/import integrity check failed")
            return False
            
    except Exception as e:
        print(f"✗ Export/import test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all comprehensive tests"""
    print("🚀 Advanced Anomaly Detection Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        ("Basic Detection", test_basic_detection),
        ("ML Detection", test_ml_detection),
        ("Real-Time Detection", test_real_time_detection),
        ("AI Insights", test_ai_insights),
        ("Performance Monitoring", test_performance_monitoring),
        ("Configuration", test_configuration),
        ("Export/Import", test_export_import)
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            results[test_name] = {
                'passed': result,
                'duration': duration,
                'error': None
            }
            
            if result:
                passed_tests += 1
                print(f"✅ {test_name} PASSED (took {duration:.2f}s)")
            else:
                print(f"❌ {test_name} FAILED (took {duration:.2f}s)")
                
        except Exception as e:
            duration = time.time() - start_time
            results[test_name] = {
                'passed': False,
                'duration': duration,
                'error': str(e)
            }
            print(f"❌ {test_name} FAILED with error: {e}")
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success rate: {passed_tests/total_tests*100:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for test_name, result in results.items():
        status = "✅ PASS" if result['passed'] else "❌ FAIL"
        duration = result['duration']
        error = f" (Error: {result['error']})" if result['error'] else ""
        print(f"  {test_name}: {status} ({duration:.2f}s){error}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! Advanced anomaly detection system is ready.")
        return True
    else:
        print(f"\n⚠️  {total_tests - passed_tests} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
