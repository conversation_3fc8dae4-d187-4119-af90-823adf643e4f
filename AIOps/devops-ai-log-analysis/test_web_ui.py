#!/usr/bin/env python3
"""
Simple test to verify the web UI is working with stack trace detection
"""

import requests
import os
import time

def test_web_ui():
    base_url = "http://localhost:5001"
    
    print("Testing DevOps AI Log Analysis Web UI...")
    print("=" * 50)
    
    # Test 1: Check if the server is running
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and responding")
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Server is not accessible: {e}")
        return False
    
    # Test 2: Test file upload and analysis
    test_log_file = "test_stack_trace_logs.txt"
    
    if not os.path.exists(test_log_file):
        print(f"❌ Test file {test_log_file} not found")
        return False
    
    try:
        with open(test_log_file, 'rb') as f:
            files = {'file': (test_log_file, f, 'text/plain')}
            response = requests.post(f"{base_url}/upload", files=files, timeout=30)
        
        if response.status_code == 200:
            print("✅ File upload successful")
            
            # Check if the response contains expected results
            if "stack trace" in response.text.lower():
                print("✅ Stack trace detection working in UI")
            else:
                print("⚠️  Stack trace detection may not be working in UI")
                
        else:
            print(f"❌ File upload failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Upload test failed: {e}")
        return False
    
    # Test 3: Test demo mode
    try:
        response = requests.get(f"{base_url}/demo", timeout=10)
        if response.status_code == 200:
            print("✅ Demo mode working")
        else:
            print(f"⚠️  Demo mode returned status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Demo mode test failed: {e}")
    
    print("\n🎉 Web UI testing completed!")
    return True

if __name__ == "__main__":
    # Wait a moment for the server to be ready
    time.sleep(2)
    
    try:
        success = test_web_ui()
        if success:
            print("\n✅ All tests passed! You can now use the web UI to test stack trace detection.")
            print("   Visit: http://localhost:5001")
            print("   Upload the test_stack_trace_logs.txt file to see the enhanced stack trace detection.")
        else:
            print("\n❌ Some tests failed. Please check the server status.")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
