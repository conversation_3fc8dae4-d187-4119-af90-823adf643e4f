#!/usr/bin/env python3
"""
Test script to verify that stack trace detection is working properly.
This test creates sample log data with stack traces and verifies that
the entire multiline error blocks are captured.
"""

import os
import sys
import tempfile

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_stack_trace_detection():
    """Test the stack trace detection functionality"""
    
    # Import the simple_log_analysis function
    from ui.simple_app import simple_log_analysis
    
    # Create test log content with various stack traces
    test_log_content = """
2024-01-15 10:30:45 INFO Starting application...
2024-01-15 10:30:46 INFO Connected to database
2024-01-15 10:30:47 ERROR Application crashed with error:
Traceback (most recent call last):
  File "main.py", line 42, in <module>
    result = process_data(data)
  File "processor.py", line 15, in process_data
    return calculate_value(data['amount'])
  File "calculator.py", line 8, in calculate_value
    return value / divisor
ZeroDivisionError: division by zero

2024-01-15 10:31:00 INFO Attempting to restart...
2024-01-15 10:31:01 ERROR Another error occurred:
Exception in thread "main" java.lang.NullPointerException
	at com.example.MyClass.process(MyClass.java:42)
	at com.example.Application.run(Application.java:123)
	at com.example.Application.main(Application.java:67)
Caused by: java.lang.IllegalStateException: Invalid state
	at com.example.StateManager.validate(StateManager.java:89)
	... 3 more

2024-01-15 10:31:15 INFO Service restarted successfully
2024-01-15 10:31:20 ERROR Python traceback in web request:
Traceback (most recent call last):
  File "/app/views.py", line 234, in handle_request
    user = User.objects.get(id=user_id)
  File "/app/models.py", line 89, in get
    return self.query_db(query)
  File "/app/database.py", line 156, in query_db
    cursor.execute(query)
AttributeError: 'NoneType' object has no attribute 'execute'

2024-01-15 10:31:30 INFO Normal log message
2024-01-15 10:31:35 WARNING Memory usage at 85%
"""
    
    print("Testing stack trace detection...")
    print("=" * 50)
    
    # Run the analysis
    result = simple_log_analysis(test_log_content)
    
    if not result['success']:
        print("❌ Analysis failed:", result.get('error', 'Unknown error'))
        return False
    
    # Check anomalies
    anomalies = result['anomalies']['anomalies_detected']
    
    print(f"Found {len(anomalies)} anomalies:")
    print("-" * 30)
    
    stack_trace_anomalies = []
    for i, anomaly in enumerate(anomalies):
        print(f"{i+1}. {anomaly['severity'].upper()}: {anomaly['message']}")
        
        if anomaly.get('details', {}).get('type') == 'multiline_stack_trace':
            stack_trace_anomalies.append(anomaly)
            details = anomaly['details']
            print(f"   Type: {details['type']}")
            print(f"   Lines: {details['line_count']}")
            print(f"   Starting line: {details['starting_line']}")
            print(f"   Main error: {details['main_error']}")
            if 'full_trace' in details:
                print(f"   Full trace preview: {details['full_trace'][:100]}...")
        else:
            print(f"   Details: {anomaly.get('details', 'None')}")
        print()
    
    # Verify that we detected stack traces
    print(f"Stack trace anomalies found: {len(stack_trace_anomalies)}")
    
    if len(stack_trace_anomalies) == 0:
        print("❌ No stack trace anomalies detected!")
        return False
    
    # Check if the full traces are captured
    success = True
    for i, anomaly in enumerate(stack_trace_anomalies):
        details = anomaly['details']
        if 'full_trace' not in details:
            print(f"❌ Stack trace {i+1} missing full_trace")
            success = False
            continue
            
        full_trace = details['full_trace']
        if not full_trace or len(full_trace.split('\n')) < 2:
            print(f"❌ Stack trace {i+1} has incomplete full_trace")
            success = False
            continue
            
        print(f"✅ Stack trace {i+1} properly captured with {len(full_trace.split('\n'))} lines")
        
        # Print the full trace for verification
        print(f"Full trace content for anomaly {i+1}:")
        print("```")
        print(full_trace)
        print("```")
        print()
    
    if success:
        print("✅ All stack trace anomalies have complete multiline content!")
    
    return success

if __name__ == "__main__":
    success = test_stack_trace_detection()
    if success:
        print("\n🎉 Stack trace detection test PASSED!")
        sys.exit(0)
    else:
        print("\n💥 Stack trace detection test FAILED!")
        sys.exit(1)
