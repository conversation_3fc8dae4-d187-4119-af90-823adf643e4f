#!/usr/bin/env python3
"""
Test script for the DevOps AI Log Analysis application.
This script tests individual components and the full pipeline.
"""

import os
import sys
import traceback

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_individual_components():
    """Test each component individually"""
    print("=== Testing Individual Components ===")
    
    # Test text ingestion
    try:
        from ingestion.ingest_text import ingest_text_logs
        print("✓ Text ingestion module imported successfully")
        
        # Test with sample data
        if os.path.exists('data/raw/text_logs.txt'):
            text_logs = ingest_text_logs('data/raw/text_logs.txt')
            print(f"✓ Text ingestion successful: {len(text_logs)} log entries")
        else:
            print("⚠ Sample text log file not found")
    except Exception as e:
        print(f"✗ Text ingestion failed: {e}")
        traceback.print_exc()
    
    # Test JSON ingestion
    try:
        from ingestion.ingest_json import ingest_json_logs
        print("✓ JSON ingestion module imported successfully")
        
        if os.path.exists('data/raw/logs.json'):
            json_logs = ingest_json_logs('data/raw/logs.json')
            print(f"✓ JSON ingestion successful: {len(json_logs)} log entries")
        else:
            print("⚠ Sample JSON log file not found")
    except Exception as e:
        print(f"✗ JSON ingestion failed: {e}")
        traceback.print_exc()
    
    # Test XML ingestion
    try:
        from ingestion.ingest_xml import ingest_xml_logs
        print("✓ XML ingestion module imported successfully")
        
        if os.path.exists('data/raw/logs.xml'):
            xml_logs = ingest_xml_logs('data/raw/logs.xml')
            print(f"✓ XML ingestion successful: {len(xml_logs)} log entries")
        else:
            print("⚠ Sample XML log file not found")
    except Exception as e:
        print(f"✗ XML ingestion failed: {e}")
        traceback.print_exc()
    
    # Test timeseries ingestion
    try:
        from ingestion.ingest_timeseries import ingest_timeseries_logs
        print("✓ Timeseries ingestion module imported successfully")
        
        if os.path.exists('data/raw/timeseries_logs.csv'):
            ts_logs = ingest_timeseries_logs('data/raw/timeseries_logs.csv')
            print(f"✓ Timeseries ingestion successful: {len(ts_logs) if ts_logs is not None else 0} log entries")
        else:
            print("⚠ Sample timeseries log file not found")
    except Exception as e:
        print(f"✗ Timeseries ingestion failed: {e}")
        traceback.print_exc()

def test_preprocessing():
    """Test preprocessing modules"""
    print("\n=== Testing Preprocessing ===")
    
    # Test text preprocessing
    try:
        from preprocessing.preprocess_text import preprocess_text
        sample_logs = ["2024-01-01 10:00:01 INFO Application started", "2024-01-01 10:00:02 ERROR Connection failed"]
        processed = preprocess_text(sample_logs)
        print(f"✓ Text preprocessing successful: {len(processed)} processed entries")
    except Exception as e:
        print(f"✗ Text preprocessing failed: {e}")
        traceback.print_exc()

def test_error_classification():
    """Test error classification"""
    print("\n=== Testing Error Classification ===")
    
    try:
        from error_classification.classify_errors import classify_errors
        sample_logs = ["ERROR: Database connection failed", "ERROR: Authentication timeout"]
        result = classify_errors(sample_logs)
        print(f"✓ Error classification successful: {result}")
    except Exception as e:
        print(f"✗ Error classification failed: {e}")
        traceback.print_exc()

def test_full_pipeline():
    """Test the complete pipeline"""
    print("\n=== Testing Full Pipeline ===")
    
    try:
        # Import all modules
        from ingestion.ingest_text import ingest_text_logs
        from ingestion.ingest_json import ingest_json_logs
        from ingestion.ingest_xml import ingest_xml_logs
        from ingestion.ingest_timeseries import ingest_timeseries_logs
        from preprocessing.preprocess_text import preprocess_text
        from preprocessing.preprocess_json import preprocess_json
        from preprocessing.preprocess_xml import preprocess_xml
        from preprocessing.preprocess_timeseries import preprocess_timeseries
        from error_classification.classify_errors import classify_errors
        from root_cause_analysis.analyze_root_cause import analyze_root_cause
        from anomaly_detection.detect_anomalies import detect_anomalies
        from trend_detection.detect_trends import detect_trends
        
        print("✓ All modules imported successfully")
        
        # Test with sample data if files exist
        if os.path.exists('data/raw/text_logs.txt'):
            print("Running full pipeline test...")
            
            # Ingest logs
            text_logs = ingest_text_logs('data/raw/text_logs.txt')
            json_logs = ingest_json_logs('data/raw/logs.json') if os.path.exists('data/raw/logs.json') else []
            xml_logs = ingest_xml_logs('data/raw/logs.xml') if os.path.exists('data/raw/logs.xml') else []
            ts_logs = ingest_timeseries_logs('data/raw/timeseries_logs.csv') if os.path.exists('data/raw/timeseries_logs.csv') else None
            
            # Preprocess
            processed_text = preprocess_text(text_logs)
            processed_json = preprocess_json(json_logs) if json_logs else []
            processed_xml = preprocess_xml(xml_logs) if xml_logs else []
            processed_ts = preprocess_timeseries(ts_logs) if ts_logs is not None else None
            
            # Classify errors
            classified_errors = classify_errors(processed_text)
            
            # Analyze root causes
            root_causes = analyze_root_cause(classified_errors)
            
            # Detect anomalies
            anomalies = detect_anomalies(processed_json)
            
            # Detect trends
            trends = detect_trends(processed_ts)
            
            print("✓ Full pipeline completed successfully!")
            print(f"- Classified Errors: {classified_errors}")
            print(f"- Root Causes: {root_causes}")
            print(f"- Anomalies: {anomalies}")
            print(f"- Trends: {trends}")
            
        else:
            print("⚠ Sample data files not found for full pipeline test")
            
    except Exception as e:
        print(f"✗ Full pipeline test failed: {e}")
        traceback.print_exc()

def main():
    """Main test function"""
    print("DevOps AI Log Analysis - Test Suite")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('src'):
        print("Error: Not in project root directory. Please run from the project root.")
        return
    
    # Run tests
    test_individual_components()
    test_preprocessing()
    test_error_classification()
    test_full_pipeline()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")

if __name__ == "__main__":
    main()
