#!/usr/bin/env python3
"""
DevOps AI Log Analysis - Root Cause Analysis Web UI
A modern web interface for log analysis and root cause detection.
"""

import os
import sys
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, flash, redirect, url_for, session
from werkzeug.utils import secure_filename
import uuid

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ingestion.ingest_text import ingest_text_logs
from ingestion.ingest_json import ingest_json_logs
from ingestion.ingest_xml import ingest_xml_logs
from preprocessing.preprocess_text import preprocess_text
from preprocessing.preprocess_json import preprocess_json
from preprocessing.preprocess_xml import preprocess_xml
from error_classification.classify_errors import classify_errors
from root_cause_analysis.analyze_root_cause import analyze_root_cause
from anomaly_detection.detect_anomalies import detect_anomalies

# Import chat interface
from chat_interface import chat_bp

app = Flask(__name__)
app.secret_key = 'dev-ops-ai-log-analysis-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Register chat blueprint
app.register_blueprint(chat_bp)

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'json', 'xml', 'csv', 'log'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_log_file(file_path, file_type):
    """Process uploaded log file through the complete pipeline"""
    try:
        # Step 1: Ingest data
        if file_type == 'text' or file_type == 'log':
            ingested_data = ingest_text_data(file_path)
        elif file_type == 'json':
            ingested_data = ingest_json_data(file_path)
        elif file_type == 'xml':
            ingested_data = ingest_xml_data(file_path)
        else:
            ingested_data = ingest_text_data(file_path)  # Default to text
        
        # Step 2: Preprocess data
        if file_type == 'text' or file_type == 'log':
            processed_data = preprocess_text_data(ingested_data)
        elif file_type == 'json':
            processed_data = preprocess_json_data(ingested_data)
        elif file_type == 'xml':
            processed_data = preprocess_xml_data(ingested_data)
        else:
            processed_data = preprocess_text_data(ingested_data)
        
        # Step 3: Classify errors
        classified_errors = classify_errors(processed_data)
        
        # Step 4: Detect anomalies
        anomalies = detect_anomalies(processed_data)
        
        # Step 5: Analyze root causes
        root_cause_analysis = analyze_root_cause(classified_errors)
        
        return {
            'success': True,
            'ingested_data': ingested_data,
            'processed_data': processed_data,
            'classified_errors': classified_errors,
            'anomalies': anomalies,
            'root_cause_analysis': root_cause_analysis,
            'file_info': {
                'name': os.path.basename(file_path),
                'size': os.path.getsize(file_path),
                'type': file_type,
                'processed_at': datetime.now().isoformat()
            }
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """Handle file upload and processing"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_id = str(uuid.uuid4())
            file_path = os.path.join(UPLOAD_FOLDER, f"{file_id}_{filename}")
            
            try:
                file.save(file_path)
                
                # Determine file type
                file_extension = filename.rsplit('.', 1)[1].lower()
                file_type = {
                    'txt': 'text',
                    'log': 'text',
                    'json': 'json',
                    'xml': 'xml',
                    'csv': 'text'
                }.get(file_extension, 'text')
                
                # Process the file
                result = process_log_file(file_path, file_type)
                
                # Store result in session for analysis page
                session['analysis_result'] = result
                session['file_id'] = file_id
                
                if result['success']:
                    flash('File uploaded and processed successfully!')
                    return redirect(url_for('analysis'))
                else:
                    flash(f'Error processing file: {result["error"]}')
                    return redirect(request.url)
                    
            except Exception as e:
                flash(f'Error uploading file: {str(e)}')
                return redirect(request.url)
            finally:
                # Clean up uploaded file
                if os.path.exists(file_path):
                    os.remove(file_path)
        else:
            flash('Invalid file type. Please upload .txt, .json, .xml, .csv, or .log files.')
            return redirect(request.url)
    
    return render_template('upload.html')

@app.route('/analysis')
def analysis():
    """Display analysis results"""
    if 'analysis_result' not in session:
        flash('No analysis data available. Please upload a file first.')
        return redirect(url_for('upload_file'))
    
    result = session['analysis_result']
    if not result['success']:
        flash(f'Analysis failed: {result["error"]}')
        return redirect(url_for('upload_file'))
    
    return render_template('analysis.html', result=result)

@app.route('/api/analysis/<analysis_id>')
def get_analysis(analysis_id):
    """API endpoint to get analysis results"""
    if 'analysis_result' not in session or session.get('file_id') != analysis_id:
        return jsonify({'error': 'Analysis not found'}), 404
    
    return jsonify(session['analysis_result'])

@app.route('/api/demo')
def demo_analysis():
    """Demo endpoint with sample data"""
    sample_data = {
        'success': True,
        'classified_errors': {
            'categories': {
                'connection_errors': [
                    'Connection refused to database server',
                    'Network timeout after 30 seconds'
                ],
                'memory_errors': [
                    'Out of memory error in application',
                    'Heap space exhausted'
                ],
                'security_errors': [
                    'Authentication failed for user admin',
                    'Permission denied accessing /var/log'
                ]
            },
            'total_errors': 6,
            'severity_scores': {'critical': 2, 'high': 3, 'medium': 1}
        },
        'anomalies': {
            'anomalies_detected': [
                {'timestamp': '2025-01-06T10:30:00', 'severity': 'high', 'message': 'Unusual spike in error rate'},
                {'timestamp': '2025-01-06T10:45:00', 'severity': 'medium', 'message': 'Memory usage anomaly detected'}
            ],
            'total_anomalies': 2
        },
        'root_cause_analysis': {
            'root_causes': {
                'connection_errors': ['network_connectivity', 'network_connectivity'],
                'memory_errors': ['resource_exhaustion', 'resource_exhaustion'],
                'security_errors': ['security_issue', 'security_issue']
            },
            'cause_frequency': {
                'network_connectivity': 2,
                'resource_exhaustion': 2,
                'security_issue': 2
            },
            'recommendations': [
                {
                    'cause': 'network_connectivity',
                    'priority': 'HIGH',
                    'action': 'Check network configuration, firewall rules, and DNS resolution',
                    'impact': '2 errors detected'
                },
                {
                    'cause': 'resource_exhaustion',
                    'priority': 'CRITICAL',
                    'action': 'Monitor resource usage, implement auto-scaling, or increase capacity',
                    'impact': '2 errors detected'
                },
                {
                    'cause': 'security_issue',
                    'priority': 'CRITICAL',
                    'action': 'Review security configurations, certificates, and access controls',
                    'impact': '2 errors detected'
                }
            ],
            'priority_scores': {
                'network_connectivity': 75,
                'resource_exhaustion': 85,
                'security_issue': 90
            },
            'summary': 'Analyzed 6 errors across 3 categories',
            'most_common_cause': ('network_connectivity', 2)
        },
        'file_info': {
            'name': 'demo_logs.txt',
            'size': 2048,
            'type': 'text',
            'processed_at': datetime.now().isoformat()
        }
    }
    
    session['analysis_result'] = sample_data
    session['file_id'] = 'demo'
    
    return jsonify(sample_data)

@app.route('/demo')
def demo():
    """Demo page with sample data"""
    demo_analysis()  # Load demo data
    return redirect(url_for('analysis'))

@app.errorhandler(413)
def too_large(e):
    flash('File too large. Maximum size is 16MB.')
    return redirect(url_for('upload_file'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
