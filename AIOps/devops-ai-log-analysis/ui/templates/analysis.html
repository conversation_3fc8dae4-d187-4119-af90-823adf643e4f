<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Results - DevOps AI Log Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
        }
        
        body {
            background-color: var(--secondary-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .analysis-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #fff, #f8fafc);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .priority-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-critical {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .priority-high {
            background: #fef3c7;
            color: #92400e;
        }
        
        .priority-medium {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .priority-low {
            background: #d1fae5;
            color: #065f46;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
        }
        
        .error-category {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .error-category h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .error-list {
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        
        .recommendation-card {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .recommendation-card h6 {
            color: #0369a1;
        }
        
        .anomaly-item {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .anomaly-item h6 {
            color: #991b1b;
        }
        
        .file-info-card {
            background: linear-gradient(135deg, var(--accent-color), #059669);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1e40af, var(--primary-color));
            transform: translateY(-1px);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }
        
        .tab-content {
            padding: 2rem 0;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6b7280;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
        }
        
        .stack-trace-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
            border: 1px solid #334155;
        }
        
        .stack-trace-block code {
            color: #e2e8f0;
            background: transparent;
            padding: 0;
            font-size: inherit;
        }
        
        .anomaly-details {
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
        }
        
        .anomaly-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .anomaly-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-robot me-2"></i>DevOps AI Log Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/upload">Upload Logs</a>
                <a class="nav-link" href="/demo">Demo</a>
                <a class="nav-link" href="/clear_session">Clear Session</a>
                <a class="nav-link" href="/debug">Debug</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>{{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="file-info-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-bold mb-2">
                                <i class="fas fa-file-alt me-2"></i>{{ result.file_info.name }}
                            </h4>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2"></i>Processed: {{ result.file_info.processed_at }}
                                <span class="ms-3">
                                    <i class="fas fa-hdd me-2"></i>Size: {{ "%.2f"|format(result.file_info.size / 1024) }} KB
                                </span>
                            </p>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-light" onclick="window.location.href='/upload'">
                                <i class="fas fa-upload me-2"></i>Upload New File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ result.classified_errors.total_errors }}</div>
                    <div class="text-muted">Total Errors</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ result.classified_errors.categories|length }}</div>
                    <div class="text-muted">Error Categories</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ result.anomalies.total_anomalies or 0 }}</div>
                    <div class="text-muted">Anomalies</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-number">{{ result.root_cause_analysis.recommendations|length }}</div>
                    <div class="text-muted">Recommendations</div>
                </div>
            </div>
        </div>

        <!-- Analysis Tabs -->
        <div class="analysis-card">
            <ul class="nav nav-tabs" id="analysisTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="root-cause-tab" data-bs-toggle="tab" data-bs-target="#root-cause" type="button" role="tab">
                        <i class="fas fa-bullseye me-2"></i>Root Cause Analysis
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="errors-tab" data-bs-toggle="tab" data-bs-target="#errors" type="button" role="tab">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error Classification
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="anomalies-tab" data-bs-toggle="tab" data-bs-target="#anomalies" type="button" role="tab">
                        <i class="fas fa-search-plus me-2"></i>Anomaly Detection
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="charts-tab" data-bs-toggle="tab" data-bs-target="#charts" type="button" role="tab">
                        <i class="fas fa-chart-bar me-2"></i>Visual Analysis
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="analysisTabContent">
                <!-- Root Cause Analysis Tab -->
                <div class="tab-pane fade show active" id="root-cause" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="fw-bold mb-3">Root Cause Summary</h5>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ result.root_cause_analysis.summary }}
                            </div>

                            {% if result.root_cause_analysis.most_common_cause %}
                            <div class="alert alert-success">
                                <i class="fas fa-bullseye me-2"></i>
                                <strong>Most Common Root Cause:</strong> {{ result.root_cause_analysis.most_common_cause[0]|title|replace('_', ' ') }}
                                ({{ result.root_cause_analysis.most_common_cause[1] }} occurrences)
                            </div>
                            {% endif %}

                            <h6 class="fw-bold mb-3">Actionable Recommendations</h6>
                            {% for rec in result.root_cause_analysis.recommendations %}
                            <div class="recommendation-card">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="fw-bold mb-0">{{ rec.cause|title|replace('_', ' ') }}</h6>
                                    <span class="priority-badge priority-{{ rec.priority.lower() }}">
                                        {{ rec.priority }}
                                    </span>
                                </div>
                                <p class="mb-2">{{ rec.action }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-impact me-1"></i>{{ rec.impact }}
                                </small>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-4">
                            <h6 class="fw-bold mb-3">Root Cause Frequency</h6>
                            {% for cause, count in result.root_cause_analysis.cause_frequency.items() %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-bold">{{ cause|title|replace('_', ' ') }}</span>
                                    <span class="small">{{ count }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ (count / result.classified_errors.total_errors * 100)|round(1) }}%"></div>
                                </div>
                            </div>
                            {% endfor %}

                            <h6 class="fw-bold mb-3 mt-4">Priority Scores</h6>
                            {% for cause, score in result.root_cause_analysis.priority_scores.items() %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="small fw-bold">{{ cause|title|replace('_', ' ') }}</span>
                                    <span class="small">{{ score|round(1) }}/100</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar 
                                        {% if score >= 80 %}bg-danger{% elif score >= 60 %}bg-warning{% else %}bg-success{% endif %}" 
                                         role="progressbar" style="width: {{ score }}%"></div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Error Classification Tab -->
                <div class="tab-pane fade" id="errors" role="tabpanel">
                    <h5 class="fw-bold mb-3">Error Classification Results</h5>
                    
                    {% for category, errors in result.classified_errors.categories.items() %}
                    {% if errors %}
                    <div class="error-category">
                        <h6 class="fw-bold">
                            <i class="fas fa-tag me-2"></i>{{ category|title|replace('_', ' ') }}
                            <span class="badge bg-primary ms-2">{{ errors|length }}</span>
                        </h6>
                        <div class="error-list">
                            {% for error in errors[:5] %}
                            <div class="mb-2 p-2 bg-white rounded border">
                                <small class="text-muted">{{ error }}</small>
                            </div>
                            {% endfor %}
                            {% if errors|length > 5 %}
                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-ellipsis-h me-1"></i>
                                    and {{ errors|length - 5 }} more errors
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Anomaly Detection Tab -->
                <div class="tab-pane fade" id="anomalies" role="tabpanel">
                    <h5 class="fw-bold mb-3">Anomaly Detection Results</h5>
                    
                    {% if result.anomalies.anomalies_detected %}
                        {% for anomaly in result.anomalies.anomalies_detected %}
                        <div class="anomaly-item">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="fw-bold mb-0">Anomaly Detected</h6>
                                <span class="priority-badge priority-{{ anomaly.severity.lower() }}">
                                    {{ anomaly.severity.upper() }}
                                </span>
                            </div>
                            <p class="mb-2">{{ anomaly.message }}</p>
                            
                            {% if anomaly.details %}
                                <div class="anomaly-details mt-3">
                                    {% if anomaly.details.type == 'multiline_stack_trace' %}
                                        <div class="alert alert-dark">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                Full Stack Trace ({{ anomaly.details.line_count }} lines)
                                            </h6>
                                            {% if anomaly.details.full_trace %}
                                                <pre class="stack-trace-block mb-0"><code>{{ anomaly.details.full_trace }}</code></pre>
                                            {% endif %}
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Starting at line {{ anomaly.details.starting_line }}
                                                </small>
                                            </div>
                                        </div>
                                    {% elif anomaly.details.type == 'critical_pattern' %}
                                        <div class="alert alert-danger">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-exclamation-circle me-2"></i>
                                                Critical Pattern Details
                                            </h6>
                                            <p class="mb-1"><strong>Pattern:</strong> {{ anomaly.details.pattern }}</p>
                                            <p class="mb-0"><strong>Content:</strong> {{ anomaly.details.line_content }}</p>
                                        </div>
                                    {% elif anomaly.details.type == 'error_frequency' %}
                                        <div class="alert alert-warning">
                                            <h6 class="fw-bold mb-2">
                                                <i class="fas fa-chart-line me-2"></i>
                                                Error Frequency Details
                                            </h6>
                                            <p class="mb-1"><strong>Category:</strong> {{ anomaly.details.category.replace('_', ' ').title() }}</p>
                                            <p class="mb-0"><strong>Count:</strong> {{ anomaly.details.count }} occurrences</p>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                            
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>{{ anomaly.timestamp }}
                            </small>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No anomalies detected in the analyzed log data.
                        </div>
                    {% endif %}
                </div>

                <!-- Visual Analysis Tab -->
                <div class="tab-pane fade" id="charts" role="tabpanel">
                    <h5 class="fw-bold mb-3">Visual Analysis</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="errorCategoryChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="rootCauseChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="priorityScoreChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <canvas id="severityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Prepare data for charts
        const errorCategories = {{ result.classified_errors.categories.keys()|list|tojson }};
        const errorCounts = {{ result.classified_errors.categories.values()|map('length')|list|tojson }};
        const rootCauseLabels = {{ result.root_cause_analysis.cause_frequency.keys()|list|tojson }};
        const rootCauseCounts = {{ result.root_cause_analysis.cause_frequency.values()|list|tojson }};
        const priorityLabels = {{ result.root_cause_analysis.priority_scores.keys()|list|tojson }};
        const priorityScores = {{ result.root_cause_analysis.priority_scores.values()|list|tojson }};
        const severityData = {{ result.classified_errors.severity_scores or {} }};

        // Color schemes
        const colorScheme = [
            '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#F97316', '#06B6D4', '#84CC16'
        ];

        // Error Category Chart
        const errorCategoryCtx = document.getElementById('errorCategoryChart').getContext('2d');
        new Chart(errorCategoryCtx, {
            type: 'doughnut',
            data: {
                labels: errorCategories.map(cat => cat.replace('_', ' ').toUpperCase()),
                datasets: [{
                    data: errorCounts,
                    backgroundColor: colorScheme.slice(0, errorCategories.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Error Categories Distribution'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Root Cause Chart
        const rootCauseCtx = document.getElementById('rootCauseChart').getContext('2d');
        new Chart(rootCauseCtx, {
            type: 'bar',
            data: {
                labels: rootCauseLabels.map(cause => cause.replace('_', ' ').toUpperCase()),
                datasets: [{
                    label: 'Frequency',
                    data: rootCauseCounts,
                    backgroundColor: colorScheme[0],
                    borderColor: colorScheme[0],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Root Cause Frequency'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Priority Score Chart
        const priorityScoreCtx = document.getElementById('priorityScoreChart').getContext('2d');
        new Chart(priorityScoreCtx, {
            type: 'radar',
            data: {
                labels: priorityLabels.map(label => label.replace('_', ' ').toUpperCase()),
                datasets: [{
                    label: 'Priority Score',
                    data: priorityScores,
                    borderColor: colorScheme[2],
                    backgroundColor: colorScheme[2] + '20',
                    pointBackgroundColor: colorScheme[2],
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: colorScheme[2]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Priority Scores'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Severity Chart
        const severityLabels = Object.keys(severityData);
        const severityValues = Object.values(severityData);
        const severityCtx = document.getElementById('severityChart').getContext('2d');
        new Chart(severityCtx, {
            type: 'pie',
            data: {
                labels: severityLabels.map(label => label.toUpperCase()),
                datasets: [{
                    data: severityValues,
                    backgroundColor: [colorScheme[1], colorScheme[3], colorScheme[2]],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Severity Distribution'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
