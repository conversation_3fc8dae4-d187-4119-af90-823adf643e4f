#!/usr/bin/env python3
"""
Simplified UI for DevOps AI Log Analysis
This version works with minimal dependencies and provides core functionality.
"""

import os
import sys
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, flash, redirect, url_for, session, send_from_directory, send_from_directory
from werkzeug.utils import secure_filename
import uuid
import re
from collections import Counter

app = Flask(__name__)
app.secret_key = 'dev-ops-ai-log-analysis-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'json', 'xml', 'csv', 'log'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def simple_log_analysis(content, file_type='text'):
    """Simple log analysis without heavy ML dependencies"""
    try:
        lines = content.split('\n') if isinstance(content, str) else content
        
        # Enhanced error patterns with stack trace support
        error_patterns = {
            'connection_errors': [
                r'connection.*refused', r'timeout.*occurred', r'host.*unreachable',
                r'network.*error', r'dns.*resolution', r'socket.*error'
            ],
            'memory_errors': [
                r'out of memory', r'heap.*overflow', r'memory.*exhausted',
                r'memory.*allocation.*failed', r'outofmemoryerror'
            ],
            'security_errors': [
                r'authentication.*failed', r'permission.*denied', r'unauthorized.*access',
                r'certificate.*invalid', r'access.*denied', r'forbidden'
            ],
            'application_errors': [
                r'null.*pointer', r'array.*bounds', r'division.*zero',
                r'assertion.*failed', r'segmentation.*fault', r'runtime.*error'
            ],
            'database_errors': [
                r'database.*connection', r'sql.*exception', r'query.*failed',
                r'table.*not.*found', r'connection.*pool'
            ],
            'stack_trace_errors': [
                r'exception.*in.*thread', r'traceback.*most recent call last',
                r'^\s*at\s+\w+\.', r'^\s*File\s+".*",\s+line\s+\d+',
                r'^\s*\w+Exception:', r'^\s*caused by:', r'stack trace:'
            ]
        }
        
        # Root cause patterns
        root_cause_patterns = {
            'configuration_error': [
                r'config.*not found', r'property.*undefined', r'missing.*configuration',
                r'invalid.*setting'
            ],
            'resource_exhaustion': [
                r'out of memory', r'disk.*full', r'no space left',
                r'resource.*unavailable', r'heap.*overflow'
            ],
            'network_connectivity': [
                r'connection.*refused', r'timeout.*occurred', r'host.*unreachable',
                r'network.*error', r'dns.*resolution'
            ],
            'dependency_failure': [
                r'service.*unavailable', r'downstream.*error', r'external.*service',
                r'api.*timeout'
            ],
            'code_defect': [
                r'null pointer', r'array.*bounds', r'division.*zero',
                r'assertion.*failed', r'segmentation.*fault'
            ],
            'security_issue': [
                r'unauthorized.*access', r'permission.*denied', r'authentication.*failed',
                r'certificate.*invalid'
            ]
        }
        
        # Classify errors
        classified_errors = {
            'categories': {},
            'total_errors': 0
        }
        
        for line in lines:
            line_lower = line.lower()
            for category, patterns in error_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line_lower):
                        if category not in classified_errors['categories']:
                            classified_errors['categories'][category] = []
                        classified_errors['categories'][category].append(line.strip())
                        classified_errors['total_errors'] += 1
                        break
        
        # Analyze root causes
        root_causes = {}
        all_causes = []
        
        for category, errors in classified_errors['categories'].items():
            category_causes = []
            for error in errors:
                error_lower = error.lower()
                found_cause = False
                
                for cause_type, patterns in root_cause_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, error_lower):
                            category_causes.append(cause_type)
                            all_causes.append(cause_type)
                            found_cause = True
                            break
                    if found_cause:
                        break
                
                if not found_cause:
                    if category == 'connection_errors':
                        category_causes.append('network_connectivity')
                        all_causes.append('network_connectivity')
                    elif category == 'memory_errors':
                        category_causes.append('resource_exhaustion')
                        all_causes.append('resource_exhaustion')
                    elif category == 'security_errors':
                        category_causes.append('security_issue')
                        all_causes.append('security_issue')
                    elif category == 'application_errors':
                        category_causes.append('code_defect')
                        all_causes.append('code_defect')
                    elif category == 'database_errors':
                        category_causes.append('dependency_failure')
                        all_causes.append('dependency_failure')
                    elif category == 'stack_trace_errors':
                        category_causes.append('code_defect')
                        all_causes.append('code_defect')
                    else:
                        category_causes.append('unknown_cause')
                        all_causes.append('unknown_cause')
            
            root_causes[category] = category_causes
        
        # Generate frequency and recommendations
        cause_frequency = Counter(all_causes)
        
        recommendations = []
        for cause, count in cause_frequency.most_common(5):
            if cause == 'network_connectivity':
                recommendations.append({
                    'cause': cause,
                    'priority': 'HIGH',
                    'action': 'Check network configuration, firewall rules, and DNS resolution',
                    'impact': f'{count} errors detected'
                })
            elif cause == 'resource_exhaustion':
                recommendations.append({
                    'cause': cause,
                    'priority': 'CRITICAL',
                    'action': 'Monitor resource usage, implement auto-scaling, or increase capacity',
                    'impact': f'{count} errors detected'
                })
            elif cause == 'configuration_error':
                recommendations.append({
                    'cause': cause,
                    'priority': 'MEDIUM',
                    'action': 'Review and validate configuration files',
                    'impact': f'{count} errors detected'
                })
            elif cause == 'code_defect':
                recommendations.append({
                    'cause': cause,
                    'priority': 'HIGH',
                    'action': 'Review code, add error handling, and implement unit tests',
                    'impact': f'{count} errors detected'
                })
            elif cause == 'security_issue':
                recommendations.append({
                    'cause': cause,
                    'priority': 'CRITICAL',
                    'action': 'Review security configurations, certificates, and access controls',
                    'impact': f'{count} errors detected'
                })
        
        # Priority scores
        priority_scores = {}
        for cause, count in cause_frequency.items():
            base_score = count * 2
            if cause in ['resource_exhaustion', 'security_issue']:
                base_score *= 2
            elif cause in ['code_defect', 'network_connectivity']:
                base_score *= 1.5
            priority_scores[cause] = min(100, base_score)
        
        # Enhanced anomaly detection with stack trace detection
        anomalies = {
            'anomalies_detected': [],
            'total_anomalies': 0
        }
        
        # Check for error spikes
        if classified_errors['total_errors'] > 10:
            anomalies['anomalies_detected'].append({
                'timestamp': datetime.now().isoformat(),
                'severity': 'high',
                'message': f'High error rate detected: {classified_errors["total_errors"]} errors'
            })
            anomalies['total_anomalies'] += 1
        
        # Detect and capture complete stack traces and traceback blocks
        stack_traces = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            line_lower = line.lower()
            
            # Check if this line starts a stack trace
            is_stack_trace_start = (
                re.search(r'traceback.*most recent call last', line_lower) or
                re.search(r'exception.*in.*thread', line_lower) or
                re.search(r'stack trace:', line_lower) or
                re.search(r'call stack:', line_lower) or
                re.search(r'backtrace:', line_lower)
            )
            
            if is_stack_trace_start:
                # Start capturing the stack trace
                stack_trace_lines = [line]
                i += 1
                
                # Continue capturing until we reach the end of the stack trace
                while i < len(lines):
                    next_line = lines[i]
                    next_line_stripped = next_line.strip()
                    
                    # Check if this line is part of the stack trace
                    is_stack_trace_line = (
                        # Python traceback lines
                        re.match(r'^\s+File\s+".*",\s+line\s+\d+', next_line) or
                        re.match(r'^\s+\w+.*', next_line) or  # Indented code/function lines
                        re.match(r'^\w+Error:', next_line) or
                        re.match(r'^\w+Exception:', next_line) or
                        # Java stack trace lines
                        re.match(r'^\s+at\s+', next_line) or
                        re.match(r'^\s+caused by:', next_line) or
                        re.match(r'^\s+\.\.\.\s*\d+\s*more', next_line) or
                        # General error continuation patterns
                        (next_line_stripped and 
                         (next_line.startswith('\t') or next_line.startswith('    ')) and
                         not re.match(r'^\s*\[?\d{4}-\d{2}-\d{2}', next_line))  # Not a new log entry
                    )
                    
                    # Stop if we hit an empty line or a line that doesn't look like part of the stack trace
                    if not next_line_stripped:
                        # Empty line might end the stack trace, but check the next line
                        if i + 1 < len(lines) and not lines[i + 1].strip():
                            break  # Two empty lines definitely end it
                        elif i + 1 < len(lines) and re.match(r'^\s*\[?\d{4}-\d{2}-\d{2}', lines[i + 1]):
                            break  # Next line is a new log entry
                    elif not is_stack_trace_line:
                        # Check if this looks like a new log entry
                        if re.match(r'^\s*\[?\d{4}-\d{2}-\d{2}', next_line):
                            break  # New log entry
                        elif re.match(r'^\w+\s+\w+\s+\d+', next_line):
                            break  # New log entry (different format)
                        elif not next_line.startswith(' ') and not next_line.startswith('\t'):
                            # Final error message (like "ValueError: something went wrong")
                            if any(keyword in next_line_stripped.lower() for keyword in 
                                   ['error:', 'exception:', 'failed:', 'traceback']):
                                stack_trace_lines.append(next_line)
                                i += 1
                            break
                    
                    if is_stack_trace_line or next_line_stripped:
                        stack_trace_lines.append(next_line)
                        i += 1
                    else:
                        break
                
                # Record the complete stack trace
                if len(stack_trace_lines) > 1:  # Must have at least 2 lines to be a real stack trace
                    # Find the main error message
                    main_error = stack_trace_lines[0]
                    for trace_line in reversed(stack_trace_lines):
                        if any(keyword in trace_line.lower() for keyword in 
                               ['error:', 'exception:', 'failed']):
                            main_error = trace_line.strip()
                            break
                    
                    stack_traces.append({
                        'type': 'stack_trace',
                        'lines': stack_trace_lines,
                        'main_error': main_error,
                        'line_number': i - len(stack_trace_lines) + 1,
                        'full_trace': '\n'.join(stack_trace_lines)
                    })
            else:
                i += 1
        
        # Add stack trace anomalies
        for trace in stack_traces:
            anomalies['anomalies_detected'].append({
                'timestamp': datetime.now().isoformat(),
                'severity': 'critical',
                'message': f'Stack trace detected: {trace["main_error"][:100]}{"..." if len(trace["main_error"]) > 100 else ""}',
                'details': {
                    'type': 'multiline_stack_trace',
                    'line_count': len(trace['lines']),
                    'starting_line': trace['line_number'],
                    'main_error': trace['main_error'],
                    'full_trace': trace['full_trace']
                }
            })
            anomalies['total_anomalies'] += 1
        
        # Check for critical patterns
        critical_patterns = ['out of memory', 'disk full', 'security violation', 'segmentation fault', 'core dumped']
        for line in lines:
            for pattern in critical_patterns:
                if pattern in line.lower():
                    anomalies['anomalies_detected'].append({
                        'timestamp': datetime.now().isoformat(),
                        'severity': 'critical',
                        'message': f'Critical pattern detected: {pattern}',
                        'details': {
                            'type': 'critical_pattern',
                            'pattern': pattern,
                            'line_content': line.strip()
                        }
                    })
                    anomalies['total_anomalies'] += 1
                    break
        
        # Check for error frequency anomalies
        error_frequency_threshold = 5
        for category, errors in classified_errors['categories'].items():
            if len(errors) >= error_frequency_threshold:
                anomalies['anomalies_detected'].append({
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'high',
                    'message': f'High frequency of {category.replace("_", " ")}: {len(errors)} occurrences',
                    'details': {
                        'type': 'error_frequency',
                        'category': category,
                        'count': len(errors)
                    }
                })
                anomalies['total_anomalies'] += 1
        
        root_cause_analysis = {
            'root_causes': root_causes,
            'cause_frequency': dict(cause_frequency),
            'recommendations': recommendations,
            'priority_scores': priority_scores,
            'summary': f'Analyzed {classified_errors["total_errors"]} errors across {len(root_causes)} categories',
            'most_common_cause': cause_frequency.most_common(1)[0] if cause_frequency else None
        }
        
        return {
            'success': True,
            'classified_errors': classified_errors,
            'anomalies': anomalies,
            'root_cause_analysis': root_cause_analysis
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def process_log_file(file_path, file_type):
    """Process uploaded log file"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Simple analysis
        analysis = simple_log_analysis(content, file_type)
        
        if analysis['success']:
            return {
                'success': True,
                'classified_errors': analysis['classified_errors'],
                'anomalies': analysis['anomalies'],
                'root_cause_analysis': analysis['root_cause_analysis'],
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': os.path.getsize(file_path),
                    'type': file_type,
                    'processed_at': datetime.now().isoformat()
                }
            }
        else:
            return analysis
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/upload', methods=['GET', 'POST'])
def upload_file():
    """Handle file upload and processing"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_id = str(uuid.uuid4())
            file_path = os.path.join(UPLOAD_FOLDER, f"{file_id}_{filename}")
            
            try:
                file.save(file_path)
                
                # Determine file type
                file_extension = filename.rsplit('.', 1)[1].lower()
                file_type = {
                    'txt': 'text',
                    'log': 'text',
                    'json': 'json',
                    'xml': 'xml',
                    'csv': 'text'
                }.get(file_extension, 'text')
                
                # Process the file
                result = process_log_file(file_path, file_type)
                
                # Debug logging
                print(f"DEBUG: Processing file {filename}")
                print(f"DEBUG: Analysis success: {result['success']}")
                if result['success']:
                    anomalies = result['anomalies']['anomalies_detected']
                    print(f"DEBUG: Found {len(anomalies)} anomalies")
                    for i, anomaly in enumerate(anomalies):
                        print(f"DEBUG: Anomaly {i+1}: {anomaly['message']}")
                        if 'details' in anomaly:
                            details = anomaly['details']
                            print(f"DEBUG: Details type: {details.get('type', 'N/A')}")
                            if 'full_trace' in details:
                                print(f"DEBUG: Full trace length: {len(details['full_trace'])} chars")
                            else:
                                print(f"DEBUG: No full_trace in details")
                        else:
                            print(f"DEBUG: No details in anomaly")
                
                # Store result in session
                session['analysis_result'] = result
                session['file_id'] = file_id
                
                if result['success']:
                    flash('File uploaded and processed successfully!')
                    return redirect(url_for('analysis'))
                else:
                    flash(f'Error processing file: {result["error"]}')
                    return redirect(request.url)
                    
            except Exception as e:
                flash(f'Error uploading file: {str(e)}')
                return redirect(request.url)
            finally:
                # Clean up uploaded file
                if os.path.exists(file_path):
                    os.remove(file_path)
        else:
            flash('Invalid file type. Please upload .txt, .json, .xml, .csv, or .log files.')
            return redirect(request.url)
    
    return render_template('upload.html')

@app.route('/analysis')
def analysis():
    """Display analysis results"""
    if 'analysis_result' not in session:
        flash('No analysis data available. Please upload a file first.')
        return redirect(url_for('upload_file'))
    
    result = session['analysis_result']
    if not result['success']:
        flash(f'Analysis failed: {result["error"]}')
        return redirect(url_for('upload_file'))
    
    return render_template('analysis.html', result=result)

@app.route('/api/demo')
def demo_analysis():
    """Demo endpoint with sample data"""
    sample_data = {
        'success': True,
        'classified_errors': {
            'categories': {
                'connection_errors': [
                    'Connection refused to database server',
                    'Network timeout after 30 seconds'
                ],
                'memory_errors': [
                    'Out of memory error in application',
                    'Heap space exhausted'
                ],
                'security_errors': [
                    'Authentication failed for user admin',
                    'Permission denied accessing /var/log'
                ],
                'stack_trace_errors': [
                    'java.lang.NullPointerException: Cannot invoke method on null object',
                    'Traceback (most recent call last): File "app.py", line 45, in process_data'
                ]
            },
            'total_errors': 8
        },
        'anomalies': {
            'anomalies_detected': [
                {
                    'timestamp': '2025-01-06T10:30:00', 
                    'severity': 'high', 
                    'message': 'High error rate detected: 8 errors'
                },
                {
                    'timestamp': '2025-01-06T10:45:00', 
                    'severity': 'critical', 
                    'message': 'Critical pattern detected: out of memory'
                },
                {
                    'timestamp': '2025-01-06T10:47:00', 
                    'severity': 'critical', 
                    'message': 'Stack trace/traceback detected: java.lang.NullPointerException: Cannot invoke method on null object',
                    'details': {
                        'type': 'multiline_stack_trace',
                        'line_count': 5,
                        'starting_line': 23,
                        'main_error': 'java.lang.NullPointerException: Cannot invoke method on null object',
                        'full_trace': '''Exception in thread "main" java.lang.NullPointerException: Cannot invoke method on null object
	at com.example.service.UserService.processRequest(UserService.java:42)
	at com.example.controller.UserController.handleRequest(UserController.java:123)
	at com.example.web.RequestHandler.dispatch(RequestHandler.java:67)
	at com.example.web.WebServer.run(WebServer.java:189)'''
                    }
                }
            ],
            'total_anomalies': 3
        },
        'root_cause_analysis': {
            'root_causes': {
                'connection_errors': ['network_connectivity', 'network_connectivity'],
                'memory_errors': ['resource_exhaustion', 'resource_exhaustion'],
                'security_errors': ['security_issue', 'security_issue'],
                'stack_trace_errors': ['code_defect', 'code_defect']
            },
            'cause_frequency': {
                'network_connectivity': 2,
                'resource_exhaustion': 2,
                'security_issue': 2,
                'code_defect': 2
            },
            'recommendations': [
                {
                    'cause': 'network_connectivity',
                    'priority': 'HIGH',
                    'action': 'Check network configuration, firewall rules, and DNS resolution',
                    'impact': '2 errors detected'
                },
                {
                    'cause': 'resource_exhaustion',
                    'priority': 'CRITICAL',
                    'action': 'Monitor resource usage, implement auto-scaling, or increase capacity',
                    'impact': '2 errors detected'
                },
                {
                    'cause': 'security_issue',
                    'priority': 'CRITICAL',
                    'action': 'Review security configurations, certificates, and access controls',
                    'impact': '2 errors detected'
                }
            ],
            'priority_scores': {
                'network_connectivity': 75,
                'resource_exhaustion': 85,
                'security_issue': 90,
                'code_defect': 80
            },
            'summary': 'Analyzed 8 errors across 4 categories',
            'most_common_cause': ('network_connectivity', 2)
        },
        'file_info': {
            'name': 'demo_logs.txt',
            'size': 2048,
            'type': 'text',
            'processed_at': datetime.now().isoformat()
        }
    }
    
    session['analysis_result'] = sample_data
    session['file_id'] = 'demo'
    
    return jsonify(sample_data)

@app.route('/demo')
def demo():
    """Demo page with sample data"""
    demo_analysis()  # Load demo data
    return redirect(url_for('analysis'))

@app.errorhandler(413)
def too_large(e):
    flash('File too large. Maximum size is 16MB.')
    return redirect(url_for('upload_file'))

@app.route('/clear_session')
def clear_session():
    """Clear session data for fresh analysis"""
    session.clear()
    flash('Session cleared. You can now upload a new file for fresh analysis.')
    return redirect(url_for('upload_file'))

@app.route('/debug')
def debug():
    """Debug route to check analysis data"""
    if 'analysis_result' not in session:
        return jsonify({'error': 'No analysis data available'})
    
    result = session['analysis_result']
    debug_info = {
        'success': result['success'],
        'anomalies_count': len(result['anomalies']['anomalies_detected']),
        'anomalies': []
    }
    
    for i, anomaly in enumerate(result['anomalies']['anomalies_detected']):
        anomaly_info = {
            'index': i,
            'message': anomaly['message'],
            'severity': anomaly['severity'],
            'has_details': 'details' in anomaly
        }
        
        if 'details' in anomaly:
            details = anomaly['details']
            anomaly_info['details'] = {
                'type': details.get('type', 'N/A'),
                'has_full_trace': 'full_trace' in details
            }
            if 'full_trace' in details:
                anomaly_info['details']['full_trace_length'] = len(details['full_trace'])
                anomaly_info['details']['full_trace_preview'] = details['full_trace'][:200] + '...' if len(details['full_trace']) > 200 else details['full_trace']
        
        debug_info['anomalies'].append(anomaly_info)
    
    return jsonify(debug_info)

@app.route('/debug_template')
def debug_template():
    """Debug template to check template rendering"""
    if 'analysis_result' not in session:
        flash('No analysis data available. Please upload a file first.')
        return redirect(url_for('upload_file'))
    
    result = session['analysis_result']
    return render_template('debug.html', result=result)

@app.route('/stack_trace_demo')
def stack_trace_demo():
    """Show a demo of the working stack trace enhancement"""
    return send_from_directory('.', 'stack_trace_demo.html')

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5001))
    app.run(debug=True, host='0.0.0.0', port=port)
