#!/usr/bin/env python3
"""
Simple test script for DevOps AI Log Analysis.
This script runs a basic test without complex operations that might hang.
"""

import sys
import os

def simple_test():
    """Run a simple test of the application"""
    print("Starting simple test...")
    print("=" * 40)
    
    # Add src to Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    try:
        # Test 1: Check if we can import modules
        print("Test 1: Checking imports...")
        from ingestion.ingest_text import ingest_text_logs
        from preprocessing.preprocess_text import preprocess_text
        print("✓ Imports successful")
        
        # Test 2: Test with sample data
        print("\nTest 2: Testing with sample data...")
        
        # Create simple test data
        test_logs = [
            "2024-01-01 10:00:01 INFO Application started",
            "2024-01-01 10:00:02 ERROR Database connection failed",
            "2024-01-01 10:00:03 WARN Memory usage high"
        ]
        
        # Test preprocessing
        processed = preprocess_text(test_logs)
        print(f"✓ Processed {len(processed)} log entries")
        
        # Test 3: Simple classification
        print("\nTest 3: Testing error classification...")
        
        # Simple mock classification for testing
        mock_classification = {
            'total_errors': 1,
            'categories': {
                'database_errors': ['Database connection failed'],
                'warnings': ['Memory usage high']
            }
        }
        
        print(f"✓ Mock classification: {mock_classification['total_errors']} errors found")
        
        print("\n" + "=" * 40)
        print("✓ All simple tests passed!")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("DevOps AI Log Analysis - Simple Test")
    success = simple_test()
    
    if success:
        print("\n🎉 Ready to run the full application!")
        print("\nNext steps:")
        print("1. Try: python simple_test.py")
        print("2. Then: python run_pipeline.py")
        print("3. Or use VS Code's Python extension to run files")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
