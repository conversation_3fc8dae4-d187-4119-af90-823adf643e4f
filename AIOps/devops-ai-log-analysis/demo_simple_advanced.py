#!/usr/bin/env python3
"""
Simple Demo: Advanced Anomaly Detection System
==============================================

This script demonstrates the enhanced anomaly detection system
with sample data, showcasing all the advanced capabilities.
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import advanced anomaly detection
try:
    from anomaly_detection.advanced_anomaly_detection import (
        detect_anomalies_advanced,
        detect_anomalies_with_monitoring,
        AdvancedAnomalyDetector,
        GenerativeAIAnalyzer,
        RealTimeAnomalyBuffer,
        configure_advanced_detection,
        performance_monitor
    )
    print("✓ Advanced anomaly detection system imported successfully")
except ImportError as e:
    print(f"✗ Failed to import advanced modules: {e}")
    sys.exit(1)

def generate_comprehensive_test_data():
    """Generate comprehensive test data showcasing different anomaly types"""
    
    print("📊 Generating comprehensive test dataset...")
    
    data = []
    base_time = datetime.now()
    
    # Normal operations (70% of data)
    normal_patterns = [
        "User authentication successful",
        "API request processed successfully",
        "Database query executed",
        "Cache hit for user session",
        "File upload completed",
        "Email notification sent",
        "Backup process completed",
        "Health check passed",
        "Configuration reloaded",
        "Session created"
    ]
    
    services = ['web-api', 'user-service', 'auth-service', 'database', 'cache', 'notification']
    
    # Add normal entries
    for i in range(700):
        timestamp = base_time + timedelta(minutes=i // 10)
        data.append({
            'timestamp': timestamp.isoformat() + 'Z',
            'level': 'INFO',
            'message': normal_patterns[i % len(normal_patterns)],
            'service': services[i % len(services)],
            'host': f"server{(i % 3) + 1}.example.com",
            'thread': f"thread-{i % 20}"
        })
    
    # Security anomalies (10% of data)
    security_patterns = [
        "Unauthorized access attempt from IP *************",
        "Multiple failed login attempts detected",
        "SQL injection attempt blocked",
        "Suspicious file upload detected",
        "Certificate validation failed",
        "Rate limit exceeded for endpoint /api/users",
        "Malicious payload detected in request",
        "Brute force attack detected",
        "Cross-site scripting attempt blocked",
        "Invalid token presented for authentication"
    ]
    
    for i in range(100):
        timestamp = base_time + timedelta(minutes=i * 7)
        data.append({
            'timestamp': timestamp.isoformat() + 'Z',
            'level': 'ERROR',
            'message': security_patterns[i % len(security_patterns)],
            'service': 'auth-service',
            'host': f"server{(i % 3) + 1}.example.com",
            'thread': f"security-thread-{i % 5}"
        })
    
    # Performance anomalies (10% of data)
    performance_patterns = [
        "Response time 8500ms exceeds threshold",
        "Database query timeout after 30 seconds",
        "Memory usage critical: 95% of heap consumed",
        "CPU utilization spike: 98% for 5 minutes",
        "Disk I/O bottleneck detected",
        "Network latency increased to 2000ms",
        "Connection pool exhausted",
        "Slow query detected: execution time 45s",
        "Memory leak detected in user service",
        "Cache miss rate above 80%"
    ]
    
    for i in range(100):
        timestamp = base_time + timedelta(minutes=i * 11)
        data.append({
            'timestamp': timestamp.isoformat() + 'Z',
            'level': 'WARN' if i % 3 == 0 else 'ERROR',
            'message': performance_patterns[i % len(performance_patterns)],
            'service': services[i % len(services)],
            'host': f"server{(i % 3) + 1}.example.com",
            'thread': f"perf-thread-{i % 10}"
        })
    
    # Critical system failures (5% of data)
    critical_patterns = [
        "OutOfMemoryError: Java heap space exhausted",
        "Service unavailable: database connection failed",
        "Fatal error: core dump generated",
        "System panic: kernel error detected",
        "Cascade failure: multiple services down",
        "Data corruption detected in user records",
        "Emergency shutdown: temperature critical",
        "Disk failure: RAID array degraded",
        "Network partition detected",
        "Service mesh failure: circuit breaker open"
    ]
    
    for i in range(50):
        timestamp = base_time + timedelta(minutes=i * 20)
        data.append({
            'timestamp': timestamp.isoformat() + 'Z',
            'level': 'CRITICAL',
            'message': critical_patterns[i % len(critical_patterns)],
            'service': services[i % len(services)],
            'host': f"server{(i % 3) + 1}.example.com",
            'thread': f"critical-thread-{i % 5}"
        })
    
    # Infrastructure anomalies (5% of data)
    infrastructure_patterns = [
        "Load balancer health check failed",
        "SSL certificate expiring in 7 days",
        "DNS resolution failure for external service",
        "Firewall rule blocking legitimate traffic",
        "Container restart loop detected",
        "Kubernetes pod eviction due to resource pressure",
        "Docker image pull timeout",
        "Service discovery failure",
        "Message queue overflow",
        "Log rotation failure"
    ]
    
    for i in range(50):
        timestamp = base_time + timedelta(minutes=i * 15)
        data.append({
            'timestamp': timestamp.isoformat() + 'Z',
            'level': 'ERROR',
            'message': infrastructure_patterns[i % len(infrastructure_patterns)],
            'service': 'infrastructure',
            'host': f"server{(i % 3) + 1}.example.com",
            'thread': f"infra-thread-{i % 8}"
        })
    
    # Sort by timestamp
    data.sort(key=lambda x: x['timestamp'])
    
    print(f"✓ Generated {len(data)} log entries with realistic anomaly patterns")
    return data

def demo_basic_vs_advanced_detection():
    """Demonstrate basic vs advanced detection capabilities"""
    
    print("\n🔍 Demo: Basic vs Advanced Anomaly Detection")
    print("=" * 60)
    
    # Generate test data
    test_data = generate_comprehensive_test_data()
    
    # Basic detection (using original detect_anomalies function)
    print("\n📊 Running basic anomaly detection...")
    start_time = time.time()
    
    try:
        from anomaly_detection.detect_anomalies import detect_anomalies as detect_basic
        basic_results = detect_basic(test_data)
        basic_time = time.time() - start_time
        
        print(f"✓ Basic detection completed in {basic_time:.3f}s")
        print(f"✓ Found {basic_results.get('count', 0)} anomalies")
        print(f"✓ Severity distribution: {basic_results.get('severity_distribution', {})}")
        
    except Exception as e:
        print(f"⚠️  Basic detection not available: {e}")
        basic_results = {'count': 0}
        basic_time = 0
    
    # Advanced detection
    print("\n🚀 Running advanced anomaly detection...")
    start_time = time.time()
    
    advanced_results = detect_anomalies_with_monitoring(
        test_data,
        use_ml=True,
        use_generative_ai=True
    )
    advanced_time = time.time() - start_time
    
    print(f"✓ Advanced detection completed in {advanced_time:.3f}s")
    print(f"✓ Found {advanced_results['count']} anomalies")
    print(f"✓ Severity distribution: {advanced_results['severity_distribution']}")
    print(f"✓ Detection methods: {list(advanced_results['detection_methods'].keys())}")
    print(f"✓ Average confidence: {advanced_results['statistics']['avg_confidence']:.2f}")
    print(f"✓ Performance: {advanced_results['performance']['throughput']:.1f} entries/sec")
    
    # Comparison
    print(f"\n📈 Comparison Results:")
    print(f"  Basic Detection:    {basic_results.get('count', 0):3d} anomalies in {basic_time:.3f}s")
    print(f"  Advanced Detection: {advanced_results['count']:3d} anomalies in {advanced_time:.3f}s")
    
    if basic_results.get('count', 0) > 0:
        improvement = (advanced_results['count'] - basic_results['count']) / basic_results['count'] * 100
        print(f"  Improvement:        {improvement:+.1f}% more anomalies detected")
    
    return advanced_results

def demo_ai_insights_and_recommendations(detection_results):
    """Demonstrate AI insights and recommendations"""
    
    print("\n🧠 Demo: AI-Powered Insights and Recommendations")
    print("=" * 60)
    
    # Display AI insights
    ai_insights = detection_results.get('ai_insights', {})
    if ai_insights:
        print("✓ AI Insights Generated:")
        
        # Error analysis
        error_analysis = ai_insights.get('error_analysis', {})
        if error_analysis:
            print(f"\n📊 Error Analysis:")
            print(f"  - Total errors: {error_analysis.get('total_errors', 0)}")
            print(f"  - Primary error category: {error_analysis.get('primary_error_category', 'N/A')}")
            print(f"  - Most affected service: {error_analysis.get('most_affected_service', 'N/A')}")
        
        # Anomaly analysis
        anomaly_analysis = ai_insights.get('anomaly_analysis', {})
        if anomaly_analysis:
            print(f"\n🔍 Anomaly Analysis:")
            print(f"  - Total anomalies: {anomaly_analysis.get('total_anomalies', 0)}")
            print(f"  - High risk anomalies: {anomaly_analysis.get('high_risk_anomalies', 0)}")
            print(f"  - Average confidence: {anomaly_analysis.get('avg_confidence', 0):.2f}")
        
        # Temporal analysis
        temporal_analysis = ai_insights.get('temporal_analysis', {})
        if temporal_analysis:
            print(f"\n⏰ Temporal Analysis:")
            print(f"  - Peak activity time: {temporal_analysis.get('peak_hour', 'N/A')}")
            print(f"  - Peak activity day: {temporal_analysis.get('peak_day', 'N/A')}")
        
        # Root cause hypotheses
        hypotheses = ai_insights.get('root_cause_hypothesis', [])
        if hypotheses:
            print(f"\n🔎 Root Cause Hypotheses:")
            for i, hypothesis in enumerate(hypotheses[:3], 1):
                confidence = hypothesis.get('confidence', 0)
                description = hypothesis.get('hypothesis', 'Unknown')
                print(f"  {i}. {description} (confidence: {confidence:.2f})")
        
        # Predictive warnings
        warnings = ai_insights.get('predictive_warnings', [])
        if warnings:
            print(f"\n⚠️  Predictive Warnings:")
            for i, warning in enumerate(warnings[:3], 1):
                description = warning.get('warning', 'Unknown')
                probability = warning.get('probability', 0)
                timeframe = warning.get('timeframe', 'Unknown')
                print(f"  {i}. {description} (probability: {probability:.2f}, timeframe: {timeframe})")
    
    # Display recommendations
    recommendations = detection_results.get('ai_recommendations', [])
    if recommendations:
        print(f"\n💡 AI Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            category = rec.get('category', 'Unknown')
            priority = rec.get('priority', 'Unknown')
            confidence = rec.get('confidence', 0)
            print(f"\n  {i}. {category.title()} Issues ({priority} Priority)")
            print(f"     Confidence: {confidence:.2f}")
            
            rec_list = rec.get('recommendations', [])
            if rec_list:
                print("     Actions:")
                for action in rec_list[:3]:
                    print(f"     • {action}")
    
    return True

def demo_real_time_capabilities():
    """Demonstrate real-time anomaly detection"""
    
    print("\n⚡ Demo: Real-Time Anomaly Detection")
    print("=" * 60)
    
    # Initialize real-time buffer
    buffer = RealTimeAnomalyBuffer(max_size=1000)
    
    print("Simulating real-time log stream...")
    
    # Simulate real-time log events
    real_time_events = [
        {'level': 'INFO', 'message': 'User login successful', 'service': 'auth-service'},
        {'level': 'INFO', 'message': 'API request processed', 'service': 'web-api'},
        {'level': 'WARN', 'message': 'High memory usage: 85%', 'service': 'user-service'},
        {'level': 'ERROR', 'message': 'Database connection timeout', 'service': 'database'},
        {'level': 'ERROR', 'message': 'Failed to process payment', 'service': 'payment-service'},
        {'level': 'CRITICAL', 'message': 'OutOfMemoryError in payment processing', 'service': 'payment-service'},
        {'level': 'ERROR', 'message': 'Unauthorized access attempt detected', 'service': 'auth-service'},
        {'level': 'CRITICAL', 'message': 'Service mesh failure detected', 'service': 'infrastructure'},
        {'level': 'ERROR', 'message': 'Circuit breaker opened for user service', 'service': 'user-service'},
        {'level': 'CRITICAL', 'message': 'Multiple service cascade failure', 'service': 'orchestrator'}
    ]
    
    for i, event in enumerate(real_time_events):
        # Add timestamp and host
        event['timestamp'] = datetime.now().isoformat() + 'Z'
        event['host'] = f"server{(i % 3) + 1}.example.com"
        
        buffer.add_entry(event)
        
        # Show real-time processing
        level_icon = {'INFO': '🟢', 'WARN': '🟡', 'ERROR': '🔴', 'CRITICAL': '🚨'}
        icon = level_icon.get(event['level'], '⚪')
        print(f"  {icon} [{event['level']}] {event['service']}: {event['message']}")
        
        time.sleep(0.2)  # Simulate real-time delays
        
        # Analyze buffer every few entries
        if (i + 1) % 3 == 0:
            recent_data = buffer.get_recent_entries(minutes=1)
            if len(recent_data) >= 3:
                mini_results = detect_anomalies_advanced(recent_data, real_time_mode=True)
                if mini_results['count'] > 0:
                    print(f"    🚨 Real-time alert: {mini_results['count']} anomalies detected!")
    
    # Final analysis of all real-time data
    print(f"\n🔍 Final real-time analysis...")
    all_recent_data = buffer.get_recent_entries(minutes=5)
    rt_results = detect_anomalies_advanced(all_recent_data, real_time_mode=True)
    
    print(f"✓ Real-time analysis found {rt_results['count']} anomalies")
    print(f"✓ Critical anomalies: {rt_results['statistics']['critical_count']}")
    print(f"✓ Processing time: {rt_results.get('performance', {}).get('detection_time', 0):.3f}s")
    
    return True

def demo_performance_scaling():
    """Demonstrate performance with different data sizes"""
    
    print("\n📊 Demo: Performance Scaling Analysis")
    print("=" * 60)
    
    data_sizes = [100, 500, 1000, 2500]
    
    print("Testing performance across different data sizes...")
    
    for size in data_sizes:
        print(f"\n📏 Testing with {size} log entries:")
        
        # Generate test data
        test_data = []
        base_time = datetime.now()
        
        for i in range(size):
            level = 'ERROR' if i % 20 == 0 else 'CRITICAL' if i % 50 == 0 else 'INFO'
            test_data.append({
                'timestamp': (base_time + timedelta(seconds=i)).isoformat() + 'Z',
                'level': level,
                'message': f'Test message {i} with various patterns',
                'service': f'service-{i % 5}',
                'host': f'server{i % 3}.example.com'
            })
        
        # Run detection with performance monitoring
        start_time = time.time()
        results = detect_anomalies_with_monitoring(test_data, use_ml=True, use_generative_ai=False)
        total_time = time.time() - start_time
        
        throughput = size / total_time if total_time > 0 else 0
        
        print(f"  ✓ Processed: {size:4d} entries")
        print(f"  ✓ Time:      {total_time:.3f}s")
        print(f"  ✓ Throughput: {throughput:.0f} entries/sec")
        print(f"  ✓ Anomalies:  {results['count']}")
        print(f"  ✓ Memory efficiency: {results['statistics']['detection_coverage']} methods used")
    
    # Show performance summary
    perf_summary = performance_monitor.get_performance_summary()
    print(f"\n📈 Overall Performance Summary:")
    print(f"  ✓ Total detections: {perf_summary['total_detections']}")
    print(f"  ✓ Average time: {perf_summary['avg_detection_time']:.3f}s")
    print(f"  ✓ Max time: {perf_summary['max_detection_time']:.3f}s")
    
    return True

def generate_final_report(detection_results):
    """Generate a comprehensive final report"""
    
    print("\n📋 Demo: Comprehensive Analysis Report")
    print("=" * 60)
    
    report = {
        'analysis_timestamp': datetime.now().isoformat(),
        'system_info': {
            'version': 'Advanced Anomaly Detection v2.0',
            'capabilities': [
                'ML-based ensemble detection',
                'Real-time processing',
                'AI-powered insights',
                'Pattern recognition',
                'Performance monitoring'
            ]
        },
        'detection_summary': {
            'total_anomalies': detection_results['count'],
            'severity_breakdown': detection_results['severity_distribution'],
            'detection_methods': detection_results['detection_methods'],
            'confidence_metrics': detection_results['statistics'],
            'performance_metrics': detection_results.get('performance_metrics', {})
        },
        'ai_analysis': detection_results.get('ai_insights', {}),
        'recommendations': detection_results.get('ai_recommendations', []),
        'system_health': {
            'status': 'HEALTHY' if detection_results['statistics']['critical_count'] < 10 else 'NEEDS_ATTENTION',
            'anomaly_rate': detection_results.get('performance_metrics', {}).get('anomaly_rate', 0),
            'critical_rate': detection_results.get('performance_metrics', {}).get('critical_rate', 0)
        }
    }
    
    # Save report
    report_filename = f"advanced_anomaly_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"✓ Comprehensive report saved to: {report_filename}")
        
        # Display key metrics
        print(f"\n📊 Key Metrics:")
        print(f"  🎯 Total Anomalies: {report['detection_summary']['total_anomalies']}")
        print(f"  🚨 Critical Issues: {detection_results['statistics']['critical_count']}")
        print(f"  🧠 AI Recommendations: {len(report['recommendations'])}")
        print(f"  💚 System Status: {report['system_health']['status']}")
        print(f"  📈 Detection Coverage: {len(report['detection_summary']['detection_methods'])} methods")
        
        return report_filename
        
    except Exception as e:
        print(f"✗ Failed to save report: {e}")
        return None

def main():
    """Run the comprehensive demo"""
    
    print("🚀 Advanced Anomaly Detection System - Comprehensive Demo")
    print("=" * 70)
    
    print(f"\nThis demo showcases the enhanced capabilities of the advanced")
    print(f"anomaly detection system, including:")
    print(f"  • ML-based ensemble detection (Isolation Forest, One-Class SVM, DBSCAN)")
    print(f"  • Advanced pattern recognition")
    print(f"  • AI-powered insights and recommendations")
    print(f"  • Real-time processing capabilities")
    print(f"  • Performance monitoring and optimization")
    print(f"  • Comprehensive reporting")
    
    try:
        # Demo 1: Basic vs Advanced Detection
        detection_results = demo_basic_vs_advanced_detection()
        
        # Demo 2: AI Insights and Recommendations
        demo_ai_insights_and_recommendations(detection_results)
        
        # Demo 3: Real-time Capabilities
        demo_real_time_capabilities()
        
        # Demo 4: Performance Scaling
        demo_performance_scaling()
        
        # Demo 5: Generate Final Report
        report_file = generate_final_report(detection_results)
        
        # Summary
        print(f"\n🎉 Demo Completed Successfully!")
        print(f"=" * 70)
        print(f"✅ All advanced features demonstrated")
        print(f"✅ Performance metrics collected")
        print(f"✅ AI insights generated")
        print(f"✅ Real-time capabilities verified")
        if report_file:
            print(f"✅ Comprehensive report saved: {report_file}")
        
        print(f"\n🚀 Your DevOps AI log analysis system now includes:")
        print(f"   🔍 Advanced ML-based anomaly detection")
        print(f"   🧠 AI-powered insights and recommendations")
        print(f"   ⚡ Real-time processing capabilities")
        print(f"   📊 Comprehensive performance monitoring")
        print(f"   🎯 Enhanced pattern recognition")
        print(f"   📋 Detailed reporting and analytics")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
